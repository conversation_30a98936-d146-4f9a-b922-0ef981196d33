# app/services/hotelbeds_service.py
import time
import hashlib
import httpx
from typing import Optional, Dict, Any
from fastapi import HTTPException
import logging
import os

logger = logging.getLogger(__name__)

# Load API credentials - will be loaded when the module is imported
def get_api_credentials():
    """Get API credentials from environment variables"""
    api_key = os.getenv('HOTELBEDS_API_KEY')
    secret = os.getenv('HOTELBEDS_SECRET')
    
    if not api_key or not secret:
        raise ValueError("HOTELBEDS_API_KEY and HOTELBEDS_SECRET environment variables must be set")
    
    return api_key, secret

# Use environment variable for URL selection
HOTELBEDS_BASE_URL = os.getenv('HOTELBEDS_BASE_URL', 'https://api.test.hotelbeds.com/hotel-content-api/1.0')

def generate_signature(api_key: str, secret: str) -> tuple[str, str]:
    """Generate Hotelbeds required X-Signature with timestamp"""
    timestamp = str(int(time.time()))
    raw_string = api_key + secret + timestamp
    signature = hashlib.sha256(raw_string.encode("utf-8")).hexdigest()
    return signature, timestamp

async def hotelbeds_request(endpoint: str, params: Optional[Dict[str, Any]] = None) -> dict:
    """Generic Hotelbeds Content API request with error handling"""
    api_key, secret = get_api_credentials()
    signature, timestamp = generate_signature(api_key, secret)

    headers = {
        "Accept": "application/json",
        "Api-key": api_key,
        "X-Signature": signature,
        "X-Timestamp": timestamp,  # Add required timestamp header
        "Accept-Encoding": "gzip"
    }

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            url = f"{HOTELBEDS_BASE_URL}{endpoint}"
            logger.info(f"Making request to: {url} with params: {params}")
            
            response = await client.get(url, headers=headers, params=params or {})
            
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 204:
                return {"message": "No content found"}
            else:
                logger.error(f"API Error: {response.status_code} - {response.text}")
                raise HTTPException(
                    status_code=response.status_code,
                    detail=f"Hotelbeds API error: {response.text}"
                )
                
    except httpx.TimeoutException:
        logger.error("Request timeout to Hotelbeds API")
        raise HTTPException(status_code=408, detail="Request timeout")
    except httpx.RequestError as e:
        logger.error(f"Request error: {str(e)}")
        raise HTTPException(status_code=503, detail="Service unavailable")
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def get_hotels_list(
    country_code: Optional[str] = None,
    state_code: Optional[str] = None,
    destination_code: Optional[str] = None,
    from_index: int = 1,
    to_index: int = 100,
    fields: str = "basic",
    language: str = "ENG"
) -> dict:
    """Get hotels list with filtering options"""
    params = {
        "from": from_index,
        "to": to_index,
        "fields": fields,
        "language": language
    }
    
    if country_code:
        params["countryCode"] = country_code
    if state_code:
        params["stateCode"] = state_code
    if destination_code:
        params["destinationCode"] = destination_code
    
    return await hotelbeds_request("/hotels", params)

async def get_hotel_details(hotel_code: int, language: str = "ENG") -> dict:
    """Get detailed information for a specific hotel"""
    params = {
        "language": language,
        "fields": "all"
    }
    return await hotelbeds_request(f"/hotels/{hotel_code}", params)

async def get_destinations(
    country_code: Optional[str] = None,
    language: str = "ENG"
) -> dict:
    """Get destinations list"""
    params = {"language": language}
    if country_code:
        params["countryCode"] = country_code
    
    return await hotelbeds_request("/locations/destinations", params)

async def get_countries(language: str = "ENG") -> dict:
    """Get countries list"""
    params = {"language": language}
    return await hotelbeds_request("/locations/countries", params)

async def get_accommodations(language: str = "ENG") -> dict:
    """Get accommodation types"""
    params = {"language": language}
    return await hotelbeds_request("/types/accommodations", params)

async def get_categories(language: str = "ENG") -> dict:
    """Get hotel categories"""
    params = {"language": language}
    return await hotelbeds_request("/types/categories", params)

async def get_chains(language: str = "ENG") -> dict:
    """Get hotel chains"""
    params = {"language": language}
    return await hotelbeds_request("/types/chains", params)

async def get_facilities(language: str = "ENG") -> dict:
    """Get hotel facilities"""
    params = {"language": language}
    return await hotelbeds_request("/types/facilities", params)
