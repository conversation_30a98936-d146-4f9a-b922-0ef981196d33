# SynTour AI性能监控系统使用指南

## 概述

SynTour AI性能监控系统提供实时的模型性能追踪、自动优化触发和详细的性能分析报告。本指南介绍如何使用和维护这个系统。

## 核心功能

### 1. 性能指标追踪

系统自动追踪以下关键指标：

#### 响应时间 (Response Time)
- **描述**: AI模型处理请求的时间
- **单位**: 秒
- **阈值**: 
  - 警告: 5秒
  - 严重: 10秒
- **优化策略**: 调整模型参数、启用缓存、优化prompt

#### Token使用量 (Token Usage)
- **描述**: 每次请求消耗的token数量
- **单位**: tokens
- **阈值**:
  - 警告: 3000 tokens
  - 严重: 4000 tokens
- **优化策略**: prompt压缩、动态截断、参数调整

#### 用户满意度 (User Satisfaction)
- **描述**: 基于用户反馈的满意度评分
- **单位**: 百分比 (0-100%)
- **阈值**:
  - 警告: 70%
  - 严重: 50%
- **优化策略**: 改进prompt工程、增强个性化

#### 错误率 (Error Rate)
- **描述**: 请求失败的百分比
- **单位**: 百分比
- **阈值**:
  - 警告: 5%
  - 严重: 10%
- **优化策略**: 改进错误处理、增强输入验证

#### 缓存命中率 (Cache Hit Rate)
- **描述**: 缓存成功命中的百分比
- **单位**: 百分比
- **阈值**:
  - 警告: 60%
  - 严重: 40%
- **优化策略**: 优化缓存策略、调整TTL设置

## API使用

### 1. 获取性能摘要

```bash
GET /api/ai/performance
```

**响应示例**:
```json
{
  "success": true,
  "performance_summary": {
    "period": "Last 24 hours",
    "timestamp": "2024-01-15T10:30:00Z",
    "metrics": {
      "response_time": {
        "current_value": 3.2,
        "sample_count": 150,
        "health": "good"
      },
      "token_usage": {
        "current_value": 2100,
        "sample_count": 150,
        "health": "good"
      },
      "user_satisfaction": {
        "current_value": 0.85,
        "sample_count": 45,
        "health": "good"
      },
      "error_rate": {
        "current_value": 0.02,
        "sample_count": 150,
        "health": "good"
      }
    },
    "alerts": [],
    "overall_health": "good"
  }
}
```

### 2. 健康状态检查

```bash
GET /health
```

**响应示例**:
```json
{
  "status": "healthy",
  "service": "fastapi-backend-enhanced",
  "genai": "connected",
  "ai_systems": {
    "prompt_engine": "active",
    "multimodal_processor": "active", 
    "performance_monitor": "active"
  }
}
```

## 自动优化机制

### 1. 触发条件

系统在以下情况自动触发优化：

- **响应时间过长**: 超过10秒触发严重警报
- **Token使用过高**: 超过4000 tokens触发优化
- **满意度下降**: 低于50%触发满意度优化
- **错误率上升**: 超过10%触发错误处理优化

### 2. 优化动作

#### 响应时间优化
```python
async def _optimize_response_time(self, current_value: float):
    # 自动执行的优化动作:
    # - 降低模型temperature以加快生成
    # - 启用响应流式传输
    # - 优化prompt模板效率
    # - 增加缓存TTL
```

#### Token使用优化
```python
async def _optimize_token_usage(self, current_value: float):
    # 自动执行的优化动作:
    # - 实施动态prompt截断
    # - 切换到更高效的prompt模板
    # - 根据请求复杂度调整max_output_tokens
```

#### 满意度优化
```python
async def _optimize_satisfaction(self, current_value: float):
    # 自动执行的优化动作:
    # - 分析用户反馈模式
    # - 改进prompt个性化
    # - 增强响应相关性评分
```

## 监控仪表板

### 1. 实时指标

通过性能API可以构建实时监控仪表板：

```javascript
// 示例：获取实时性能数据
async function fetchPerformanceData() {
    const response = await fetch('/api/ai/performance');
    const data = await response.json();
    
    // 更新仪表板图表
    updateResponseTimeChart(data.performance_summary.metrics.response_time);
    updateTokenUsageChart(data.performance_summary.metrics.token_usage);
    updateSatisfactionChart(data.performance_summary.metrics.user_satisfaction);
}

// 每30秒更新一次
setInterval(fetchPerformanceData, 30000);
```

### 2. 警报通知

系统生成的警报包含：

```json
{
  "metric": "response_time",
  "severity": "warning",
  "message": "WARNING: Response Time is 7.50s (threshold: 5s)",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

## 数据分析

### 1. 趋势分析

```python
# 获取24小时性能趋势
summary = performance_monitor.get_performance_summary(hours=24)

# 分析响应时间趋势
response_times = summary['metrics']['response_time']
if response_times['health'] == 'warning':
    print("响应时间呈上升趋势，需要关注")
```

### 2. 性能报告

定期生成性能报告：

```python
# 生成周报
weekly_summary = performance_monitor.get_performance_summary(hours=168)

# 关键指标
print(f"平均响应时间: {weekly_summary['metrics']['response_time']['current_value']:.2f}s")
print(f"平均Token使用: {weekly_summary['metrics']['token_usage']['current_value']:.0f}")
print(f"用户满意度: {weekly_summary['metrics']['user_satisfaction']['current_value']:.1%}")
```

## 维护操作

### 1. 数据清理

```python
# 清理7天前的旧数据
await performance_monitor.cleanup_old_data(days=7)
```

### 2. 阈值调整

根据实际使用情况调整性能阈值：

```python
# 在model_performance.py中修改
self.thresholds = {
    PerformanceMetric.RESPONSE_TIME: {
        "warning": 6.0,    # 从5.0调整到6.0
        "critical": 12.0   # 从10.0调整到12.0
    }
}
```

### 3. 手动触发优化

```python
# 手动触发特定优化
await performance_monitor._trigger_optimization(
    PerformanceMetric.RESPONSE_TIME, 
    current_value=8.5
)
```

## 最佳实践

### 1. 监控频率
- **实时监控**: 每30秒检查一次关键指标
- **详细分析**: 每小时生成性能摘要
- **趋势分析**: 每日分析24小时趋势
- **深度报告**: 每周生成综合性能报告

### 2. 警报处理
- **立即响应**: 严重级别警报需要立即处理
- **定期检查**: 警告级别警报需要定期检查
- **趋势关注**: 关注连续多次的警告信号

### 3. 优化策略
- **渐进式优化**: 避免一次性大幅调整参数
- **A/B测试**: 对比优化前后的效果
- **用户反馈**: 结合用户反馈验证优化效果

### 4. 数据保留
- **实时数据**: 保留24小时
- **汇总数据**: 保留7天
- **历史趋势**: 保留30天的关键指标

## 故障排除

### 常见问题

1. **性能数据缺失**
   - 检查是否有API调用
   - 验证监控系统是否正常初始化

2. **警报过于频繁**
   - 调整阈值设置
   - 增加警报冷却时间

3. **自动优化无效**
   - 检查优化策略实现
   - 验证参数调整是否生效

4. **内存使用过高**
   - 减少数据保留时间
   - 增加清理频率

通过合理使用性能监控系统，可以确保SynTour AI系统始终保持最佳性能状态，为用户提供优质的旅游规划服务。
