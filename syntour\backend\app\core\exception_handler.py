"""
Enhanced Exception Handling System for SynTour
Provides comprehensive error tracking, classification, and recovery mechanisms
"""

import traceback
import uuid
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass, asdict
import logging
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

logger = logging.getLogger('syntour.exceptions')

class ErrorSeverity(Enum):
    """Error severity levels for classification and alerting"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class ErrorCategory(Enum):
    """Error categories for better classification and handling"""
    VALIDATION = "validation"
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_API = "external_api"
    DATABASE = "database"
    NETWORK = "network"
    SYSTEM = "system"
    AI_MODEL = "ai_model"
    FILE_PROCESSING = "file_processing"

@dataclass
class ErrorContext:
    """Structured error context information"""
    error_id: str
    timestamp: datetime
    request_id: Optional[str] = None
    user_id: Optional[str] = None
    endpoint: Optional[str] = None
    method: Optional[str] = None
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None

class SynTourException(Exception):
    """
    Base exception class for SynTour application
    Provides structured error information and context
    """
    
    def __init__(
        self,
        message: str,
        error_code: str,
        category: ErrorCategory = ErrorCategory.SYSTEM,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        recoverable: bool = True,
        context: Optional[ErrorContext] = None,
        original_exception: Optional[Exception] = None,
        user_message: Optional[str] = None
    ):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.category = category
        self.severity = severity
        self.recoverable = recoverable
        self.context = context or ErrorContext(
            error_id=str(uuid.uuid4()),
            timestamp=datetime.now()
        )
        self.original_exception = original_exception
        self.user_message = user_message or self._generate_user_message()
        self.traceback_str = traceback.format_exc()
    
    def _generate_user_message(self) -> str:
        """Generate user-friendly error message based on category"""
        user_messages = {
            ErrorCategory.VALIDATION: "Please check your input and try again.",
            ErrorCategory.AUTHENTICATION: "Authentication failed. Please log in again.",
            ErrorCategory.AUTHORIZATION: "You don't have permission to perform this action.",
            ErrorCategory.EXTERNAL_API: "External service is temporarily unavailable. Please try again later.",
            ErrorCategory.DATABASE: "Database operation failed. Please try again.",
            ErrorCategory.NETWORK: "Network connection issue. Please check your connection.",
            ErrorCategory.AI_MODEL: "AI service is temporarily unavailable. Please try again.",
            ErrorCategory.FILE_PROCESSING: "File processing failed. Please check the file format.",
            ErrorCategory.BUSINESS_LOGIC: "Operation could not be completed due to business rules.",
            ErrorCategory.SYSTEM: "An unexpected error occurred. Please try again."
        }
        return user_messages.get(self.category, "An error occurred. Please try again.")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for logging and API responses"""
        return {
            "error_id": self.context.error_id,
            "message": self.message,
            "error_code": self.error_code,
            "category": self.category.value,
            "severity": self.severity.value,
            "recoverable": self.recoverable,
            "user_message": self.user_message,
            "timestamp": self.context.timestamp.isoformat(),
            "context": asdict(self.context) if self.context else None,
            "original_exception": str(self.original_exception) if self.original_exception else None
        }

# Specific exception classes
class ValidationException(SynTourException):
    """Exception for input validation errors"""
    def __init__(self, message: str, field: str = None, **kwargs):
        super().__init__(
            message=message,
            error_code="VALIDATION_ERROR",
            category=ErrorCategory.VALIDATION,
            severity=ErrorSeverity.LOW,
            **kwargs
        )
        self.field = field

class AuthenticationException(SynTourException):
    """Exception for authentication failures"""
    def __init__(self, message: str = "Authentication failed", **kwargs):
        super().__init__(
            message=message,
            error_code="AUTH_ERROR",
            category=ErrorCategory.AUTHENTICATION,
            severity=ErrorSeverity.MEDIUM,
            recoverable=False,
            **kwargs
        )

class ExternalAPIException(SynTourException):
    """Exception for external API failures"""
    def __init__(self, service: str, message: str, status_code: int = None, **kwargs):
        super().__init__(
            message=f"{service} API error: {message}",
            error_code="EXTERNAL_API_ERROR",
            category=ErrorCategory.EXTERNAL_API,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.service = service
        self.status_code = status_code

class AIModelException(SynTourException):
    """Exception for AI model processing errors"""
    def __init__(self, model: str, operation: str, message: str, **kwargs):
        super().__init__(
            message=f"AI model {model} error in {operation}: {message}",
            error_code="AI_MODEL_ERROR",
            category=ErrorCategory.AI_MODEL,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.model = model
        self.operation = operation

class DatabaseException(SynTourException):
    """Exception for database operation errors"""
    def __init__(self, operation: str, message: str, **kwargs):
        super().__init__(
            message=f"Database {operation} error: {message}",
            error_code="DATABASE_ERROR",
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            **kwargs
        )
        self.operation = operation

class ExceptionHandler:
    """
    Centralized exception handler with recovery strategies
    and comprehensive error tracking
    """
    
    def __init__(self):
        self.error_stats: Dict[str, int] = {}
        self.recovery_strategies: Dict[ErrorCategory, List[Callable]] = {}
        self._setup_recovery_strategies()
    
    def _setup_recovery_strategies(self):
        """Setup recovery strategies for different error categories"""
        self.recovery_strategies = {
            ErrorCategory.EXTERNAL_API: [
                self._retry_with_backoff,
                self._use_cached_response,
                self._fallback_to_alternative_service
            ],
            ErrorCategory.AI_MODEL: [
                self._retry_with_simpler_prompt,
                self._use_fallback_model,
                self._return_cached_response
            ],
            ErrorCategory.DATABASE: [
                self._retry_database_operation,
                self._use_read_replica,
                self._return_cached_data
            ]
        }
    
    async def handle_exception(
        self,
        exc: Exception,
        request: Optional[Request] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """
        Main exception handling method
        Processes exceptions and returns appropriate responses
        """
        
        # Create error context from request
        error_context = self._create_error_context(request, context)
        
        # Convert to SynTourException if needed
        if not isinstance(exc, SynTourException):
            exc = self._convert_to_syntour_exception(exc, error_context)
        else:
            exc.context = error_context
        
        # Log the exception
        await self._log_exception(exc)
        
        # Update error statistics
        self._update_error_stats(exc)
        
        # Attempt recovery if possible
        recovery_result = await self._attempt_recovery(exc)
        if recovery_result:
            return recovery_result
        
        # Return error response
        return self._create_error_response(exc)
    
    def _create_error_context(
        self,
        request: Optional[Request],
        additional_context: Optional[Dict[str, Any]]
    ) -> ErrorContext:
        """Create error context from request and additional data"""
        
        context = ErrorContext(
            error_id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            additional_data=additional_context or {}
        )
        
        if request:
            context.request_id = getattr(request.state, 'request_id', None)
            context.endpoint = str(request.url.path)
            context.method = request.method
            context.user_agent = request.headers.get('user-agent')
            context.ip_address = request.client.host if request.client else None
        
        return context
    
    def _convert_to_syntour_exception(
        self,
        exc: Exception,
        context: ErrorContext
    ) -> SynTourException:
        """Convert generic exceptions to SynTourException"""
        
        if isinstance(exc, HTTPException):
            return SynTourException(
                message=exc.detail,
                error_code=f"HTTP_{exc.status_code}",
                category=ErrorCategory.VALIDATION if exc.status_code < 500 else ErrorCategory.SYSTEM,
                severity=ErrorSeverity.LOW if exc.status_code < 500 else ErrorSeverity.HIGH,
                context=context,
                original_exception=exc
            )
        
        elif isinstance(exc, ValueError):
            return ValidationException(
                message=str(exc),
                context=context,
                original_exception=exc
            )
        
        elif isinstance(exc, ConnectionError):
            return SynTourException(
                message=str(exc),
                error_code="CONNECTION_ERROR",
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.HIGH,
                context=context,
                original_exception=exc
            )
        
        else:
            return SynTourException(
                message=str(exc),
                error_code="UNKNOWN_ERROR",
                category=ErrorCategory.SYSTEM,
                severity=ErrorSeverity.HIGH,
                context=context,
                original_exception=exc
            )
    
    async def _log_exception(self, exc: SynTourException):
        """Log exception with appropriate level and context"""
        
        log_data = exc.to_dict()
        
        if exc.severity == ErrorSeverity.CRITICAL:
            logger.critical(
                f"Critical error: {exc.message}",
                extra={'extra_context': log_data, 'request_id': exc.context.request_id}
            )
        elif exc.severity == ErrorSeverity.HIGH:
            logger.error(
                f"High severity error: {exc.message}",
                extra={'extra_context': log_data, 'request_id': exc.context.request_id}
            )
        elif exc.severity == ErrorSeverity.MEDIUM:
            logger.warning(
                f"Medium severity error: {exc.message}",
                extra={'extra_context': log_data, 'request_id': exc.context.request_id}
            )
        else:
            logger.info(
                f"Low severity error: {exc.message}",
                extra={'extra_context': log_data, 'request_id': exc.context.request_id}
            )
    
    def _update_error_stats(self, exc: SynTourException):
        """Update error statistics for monitoring"""
        key = f"{exc.category.value}:{exc.error_code}"
        self.error_stats[key] = self.error_stats.get(key, 0) + 1
    
    async def _attempt_recovery(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Attempt to recover from the error using configured strategies"""
        
        if not exc.recoverable:
            return None
        
        strategies = self.recovery_strategies.get(exc.category, [])
        
        for strategy in strategies:
            try:
                result = await strategy(exc)
                if result:
                    logger.info(
                        f"Successfully recovered from error using {strategy.__name__}",
                        extra={'request_id': exc.context.request_id}
                    )
                    return result
            except Exception as recovery_exc:
                logger.warning(
                    f"Recovery strategy {strategy.__name__} failed: {recovery_exc}",
                    extra={'request_id': exc.context.request_id}
                )
        
        return None
    
    def _create_error_response(self, exc: SynTourException) -> JSONResponse:
        """Create JSON error response"""
        
        status_code = 500
        if exc.category == ErrorCategory.VALIDATION:
            status_code = 400
        elif exc.category == ErrorCategory.AUTHENTICATION:
            status_code = 401
        elif exc.category == ErrorCategory.AUTHORIZATION:
            status_code = 403
        elif exc.category == ErrorCategory.EXTERNAL_API:
            status_code = 502
        
        response_data = {
            "success": False,
            "error": {
                "id": exc.context.error_id,
                "message": exc.user_message,
                "code": exc.error_code,
                "category": exc.category.value,
                "recoverable": exc.recoverable,
                "timestamp": exc.context.timestamp.isoformat()
            }
        }
        
        # Add debug information in development
        if logger.isEnabledFor(logging.DEBUG):
            response_data["debug"] = {
                "technical_message": exc.message,
                "traceback": exc.traceback_str
            }
        
        return JSONResponse(
            status_code=status_code,
            content=response_data
        )
    
    # Recovery strategy implementations
    async def _retry_with_backoff(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Retry operation with exponential backoff"""
        # Implementation would depend on the specific operation
        return None
    
    async def _use_cached_response(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Return cached response if available"""
        # Implementation would check cache for previous successful response
        return None
    
    async def _fallback_to_alternative_service(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Use alternative service if primary fails"""
        # Implementation would switch to backup service
        return None
    
    async def _retry_with_simpler_prompt(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Retry AI operation with simpler prompt"""
        # Implementation would simplify the prompt and retry
        return None
    
    async def _use_fallback_model(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Use fallback AI model"""
        # Implementation would switch to backup model
        return None
    
    async def _return_cached_response(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Return cached AI response"""
        # Implementation would return cached AI response
        return None
    
    async def _retry_database_operation(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Retry database operation"""
        # Implementation would retry database operation
        return None
    
    async def _use_read_replica(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Use read replica for database operations"""
        # Implementation would switch to read replica
        return None
    
    async def _return_cached_data(self, exc: SynTourException) -> Optional[JSONResponse]:
        """Return cached database data"""
        # Implementation would return cached data
        return None
    
    def get_error_stats(self) -> Dict[str, int]:
        """Get current error statistics"""
        return self.error_stats.copy()
    
    def reset_error_stats(self):
        """Reset error statistics"""
        self.error_stats.clear()

# Global exception handler instance
exception_handler = ExceptionHandler()
