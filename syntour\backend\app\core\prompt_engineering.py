"""
Advanced Prompt Engineering System for SynTour AI
Implements intelligent, context-aware prompt generation with specialized templates
"""

from typing import Dict, Any, List, Optional
from enum import Enum
from dataclasses import dataclass
import logging
import json

logger = logging.getLogger(__name__)

class PromptType(Enum):
    """Enumeration of specialized prompt types for different use cases"""
    TRAVEL_PLANNING = "travel_planning"
    DESTINATION_INFO = "destination_info"
    CULTURAL_GUIDANCE = "cultural_guidance"
    BUDGET_OPTIMIZATION = "budget_optimization"
    EMERGENCY_ASSISTANCE = "emergency_assistance"
    MULTIMODAL_ANALYSIS = "multimodal_analysis"
    GENERAL_CHAT = "general_chat"

@dataclass
class PromptTemplate:
    """Template structure for prompt generation"""
    system_prompt: str
    user_prompt_template: str
    examples: List[Dict[str, str]]
    parameters: Dict[str, Any]

class AdvancedPromptEngine:
    """
    Advanced prompt engineering system with context-aware generation
    Provides specialized prompts for different travel planning scenarios
    """
    
    def __init__(self):
        self.prompt_templates = self._initialize_templates()
        self.context_enhancers = {
            "location": self._enhance_location_context,
            "cultural": self._enhance_cultural_context,
            "seasonal": self._enhance_seasonal_context,
            "budget": self._enhance_budget_context,
            "user_profile": self._enhance_user_profile_context
        }
        logger.info("Advanced Prompt Engine initialized with specialized templates")
    
    def _initialize_templates(self) -> Dict[PromptType, PromptTemplate]:
        """Initialize specialized prompt templates for different use cases"""
        return {
            PromptType.TRAVEL_PLANNING: PromptTemplate(
                system_prompt="""You are SynTour AI, Malaysia's premier travel planning specialist with deep expertise in:

CORE COMPETENCIES:
• Comprehensive Malaysian geography, culture, and tourism infrastructure
• Multi-destination itinerary optimization with transportation logistics
• Budget-conscious planning with cost-benefit analysis
• Cultural sensitivity and sustainable tourism practices
• Real-time travel conditions and seasonal considerations

PLANNING METHODOLOGY:
1. Analyze user preferences, constraints, and travel style
2. Consider seasonal factors, local events, and weather patterns
3. Optimize for time efficiency and budget effectiveness
4. Include cultural immersion opportunities and local experiences
5. Provide practical logistics and safety considerations

RESPONSE STRUCTURE:
• Executive Summary (key highlights and recommendations)
• Detailed Itinerary (day-by-day with timing and logistics)
• Budget Breakdown (transparent cost analysis)
• Cultural Insights (etiquette, customs, language tips)
• Practical Information (transportation, accommodation, dining)
• Safety and Emergency Information

Always provide specific, actionable advice with exact locations, timing, and cost estimates where possible.""",
                
                user_prompt_template="""TRAVEL PLANNING REQUEST:
Destination(s): {destinations}
Duration: {duration}
Budget Range: {budget}
Travel Style: {travel_style}
Interests: {interests}
Special Requirements: {special_requirements}

User Query: {user_message}

Please create a comprehensive travel plan following the methodology outlined in your system instructions.""",
                
                examples=[
                    {
                        "user": "Plan a 5-day budget trip to Penang for food lovers",
                        "assistant": "# 5-Day Penang Food Adventure (Budget: RM800-1200)\n\n## Executive Summary\nPenang offers Malaysia's best street food scene with UNESCO heritage charm. This itinerary focuses on authentic local experiences while maintaining budget consciousness.\n\n## Day-by-Day Itinerary\n### Day 1: Georgetown Heritage & Street Food Introduction..."
                    }
                ],
                
                parameters={
                    "temperature": 0.3,  # Lower for more structured planning
                    "max_tokens": 2000,
                    "top_p": 0.9
                }
            ),
            
            PromptType.DESTINATION_INFO: PromptTemplate(
                system_prompt="""You are SynTour AI's Destination Information Specialist, expert in:

DESTINATION EXPERTISE:
• Detailed knowledge of Malaysian states, cities, and attractions
• Historical and cultural significance of locations
• Practical visitor information and accessibility
• Seasonal considerations and optimal visit times
• Hidden gems and off-the-beaten-path discoveries

INFORMATION DELIVERY:
• Provide comprehensive yet digestible information
• Include practical details (opening hours, costs, accessibility)
• Mention cultural significance and local customs
• Suggest complementary nearby attractions
• Offer tips for different types of travelers

RESPONSE APPROACH:
• Start with key highlights and must-know facts
• Provide detailed descriptions with vivid imagery
• Include practical visitor information
• Suggest optimal visit strategies
• End with insider tips and recommendations""",
                
                user_prompt_template="""DESTINATION INFORMATION REQUEST:
Location: {location}
Specific Interest: {specific_interest}
Travel Context: {travel_context}

User Query: {user_message}

Please provide comprehensive destination information following your expertise guidelines.""",
                
                examples=[],
                
                parameters={
                    "temperature": 0.4,
                    "max_tokens": 1500,
                    "top_p": 0.85
                }
            ),
            
            PromptType.MULTIMODAL_ANALYSIS: PromptTemplate(
                system_prompt="""You are SynTour AI's Visual Analysis Specialist, expert in:

VISUAL ANALYSIS CAPABILITIES:
• Malaysian landmark and destination identification
• Cultural context interpretation from images
• Travel photography composition analysis
• Safety and accessibility assessment from visuals
• Local customs and etiquette guidance based on visual cues

ANALYSIS FRAMEWORK:
1. Identify location, landmarks, or cultural elements
2. Assess travel relevance and tourist potential
3. Provide cultural context and significance
4. Suggest related destinations or experiences
5. Offer practical travel advice based on visual information

RESPONSE APPROACH:
• Be specific about what you observe
• Connect visual elements to travel opportunities
• Provide cultural education and sensitivity guidance
• Suggest practical next steps for travelers""",
                
                user_prompt_template="""VISUAL ANALYSIS REQUEST:
Image Description: {image_description}
User Context: {user_context}
Specific Question: {user_message}

Please analyze the visual content and provide travel-relevant insights following your analysis framework.""",
                
                examples=[],
                
                parameters={
                    "temperature": 0.4,
                    "max_tokens": 1500,
                    "top_p": 0.85
                }
            ),
            
            PromptType.CULTURAL_GUIDANCE: PromptTemplate(
                system_prompt="""You are SynTour AI's Cultural Sensitivity Advisor, specializing in:

CULTURAL EXPERTISE:
• Malaysian multicultural society (Malay, Chinese, Indian, Indigenous)
• Religious practices and customs (Islam, Buddhism, Hinduism, Christianity)
• Social etiquette and behavioral norms
• Language considerations and basic phrases
• Appropriate dress codes for different contexts

GUIDANCE PRINCIPLES:
• Promote respectful and responsible tourism
• Educate about cultural significance and history
• Provide practical do's and don'ts
• Encourage meaningful cultural exchange
• Address common cultural misunderstandings

RESPONSE STYLE:
• Educational yet accessible tone
• Specific examples and scenarios
• Practical tips for real situations
• Respectful explanation of cultural practices
• Encouragement of cultural appreciation""",
                
                user_prompt_template="""CULTURAL GUIDANCE REQUEST:
Cultural Context: {cultural_context}
Situation: {situation}
User Background: {user_background}

User Query: {user_message}

Please provide culturally sensitive guidance following your expertise principles.""",
                
                examples=[],
                
                parameters={
                    "temperature": 0.3,
                    "max_tokens": 1200,
                    "top_p": 0.8
                }
            ),
            
            PromptType.GENERAL_CHAT: PromptTemplate(
                system_prompt="""You are SynTour AI, a friendly and knowledgeable travel assistant specializing in Malaysia tourism.

PERSONALITY:
• Enthusiastic and passionate about travel
• Culturally sensitive and respectful
• Helpful and solution-oriented
• Friendly and approachable
• Adaptable to different communication styles

CAPABILITIES:
• General travel advice and recommendations
• Quick answers to travel questions
• Friendly conversation about travel experiences
• Basic information about Malaysian destinations
• Encouragement and inspiration for travel planning

RESPONSE STYLE:
• Conversational and engaging
• Provide helpful information naturally
• Ask follow-up questions when appropriate
• Show genuine interest in user's travel goals
• Maintain professional yet friendly tone""",
                
                user_prompt_template="""GENERAL CONVERSATION:
User Message: {user_message}
Context: {context}

Please respond in a friendly, helpful manner while staying focused on travel-related topics.""",
                
                examples=[],
                
                parameters={
                    "temperature": 0.7,
                    "max_tokens": 1000,
                    "top_p": 0.9
                }
            )
        }
    
    def determine_prompt_type(self, user_message: str, context: Dict[str, Any] = None) -> PromptType:
        """
        Intelligently determine the most appropriate prompt type based on user input
        
        Args:
            user_message: The user's input message
            context: Additional context information
            
        Returns:
            PromptType: The most suitable prompt type for the request
        """
        message_lower = user_message.lower()
        
        # Travel planning keywords
        planning_keywords = [
            "plan", "itinerary", "trip", "vacation", "holiday", "travel plan",
            "schedule", "organize", "arrange", "book", "visit order"
        ]
        if any(keyword in message_lower for keyword in planning_keywords):
            return PromptType.TRAVEL_PLANNING
        
        # Destination information keywords
        destination_keywords = [
            "about", "information", "tell me about", "describe", "what is",
            "where is", "how to get", "opening hours", "cost", "price"
        ]
        if any(keyword in message_lower for keyword in destination_keywords):
            return PromptType.DESTINATION_INFO
        
        # Cultural guidance keywords
        cultural_keywords = [
            "culture", "custom", "tradition", "etiquette", "dress code",
            "religious", "respect", "appropriate", "behavior", "language"
        ]
        if any(keyword in message_lower for keyword in cultural_keywords):
            return PromptType.CULTURAL_GUIDANCE
        
        # Budget optimization keywords
        budget_keywords = [
            "budget", "cheap", "affordable", "cost", "price", "money",
            "expensive", "save", "discount", "deal"
        ]
        if any(keyword in message_lower for keyword in budget_keywords):
            return PromptType.BUDGET_OPTIMIZATION
        
        # Check for multimodal context
        if context and ("image_description" in context or "file_analysis" in context):
            return PromptType.MULTIMODAL_ANALYSIS
        
        # Default to general chat for casual conversations
        return PromptType.GENERAL_CHAT
    
    def generate_enhanced_prompt(
        self,
        prompt_type: PromptType,
        user_message: str,
        context: Dict[str, Any] = None,
        user_preferences: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Generate an enhanced prompt with context awareness and personalization
        
        Args:
            prompt_type: The type of prompt to generate
            user_message: The user's input message
            context: Additional context information
            user_preferences: User preferences and profile data
            
        Returns:
            Dict containing the generated prompt and configuration
        """
        template = self.prompt_templates.get(prompt_type)
        if not template:
            logger.warning(f"No template found for prompt type: {prompt_type}")
            return self._generate_fallback_prompt(user_message)
        
        # Start with base system prompt
        system_prompt = template.system_prompt
        
        # Apply context enhancers
        if context:
            enhanced_context = self._apply_context_enhancers(context)
            if enhanced_context:
                system_prompt += f"\n\nCONTEXT INFORMATION:\n{enhanced_context}"
        
        # Add user preferences
        if user_preferences:
            preference_context = self._format_user_preferences(user_preferences)
            system_prompt += f"\n\nUSER PREFERENCES:\n{preference_context}"
        
        # Generate user prompt
        try:
            user_prompt = template.user_prompt_template.format(
                user_message=user_message,
                **self._extract_template_variables(context or {})
            )
        except KeyError as e:
            logger.warning(f"Missing template variable: {e}")
            user_prompt = f"User Query: {user_message}"
        
        return {
            "system_prompt": system_prompt,
            "user_prompt": user_prompt,
            "parameters": template.parameters,
            "examples": template.examples,
            "prompt_type": prompt_type.value
        }
    
    def _apply_context_enhancers(self, context: Dict[str, Any]) -> str:
        """Apply context enhancers to enrich the prompt"""
        enhanced_parts = []
        
        for context_type, enhancer in self.context_enhancers.items():
            if context_type in context:
                enhancement = enhancer(context[context_type])
                if enhancement:
                    enhanced_parts.append(enhancement)
        
        return "\n".join(enhanced_parts)
    
    def _enhance_location_context(self, location_data: Dict[str, Any]) -> str:
        """Enhance location-specific context"""
        if not location_data:
            return ""
        
        context_parts = []
        
        if "current_location" in location_data:
            context_parts.append(f"User's current location: {location_data['current_location']}")
        
        if "preferred_regions" in location_data:
            regions = ", ".join(location_data["preferred_regions"])
            context_parts.append(f"Preferred regions: {regions}")
        
        if "travel_radius" in location_data:
            context_parts.append(f"Willing to travel within: {location_data['travel_radius']} km")
        
        return "LOCATION CONTEXT:\n" + "\n".join(context_parts) if context_parts else ""
    
    def _enhance_seasonal_context(self, seasonal_data: Dict[str, Any]) -> str:
        """Enhance seasonal and temporal context"""
        if not seasonal_data:
            return ""
        
        current_season = seasonal_data.get("current_season", "")
        weather_conditions = seasonal_data.get("weather", "")
        local_events = seasonal_data.get("events", [])
        
        context_parts = [
            f"Current season: {current_season}",
            f"Weather conditions: {weather_conditions}"
        ]
        
        if local_events:
            events_str = ", ".join(local_events)
            context_parts.append(f"Local events/festivals: {events_str}")
        
        return "SEASONAL CONTEXT:\n" + "\n".join(context_parts)
    
    def _enhance_cultural_context(self, cultural_data: Dict[str, Any]) -> str:
        """Enhance cultural awareness context"""
        if not cultural_data:
            return ""
        
        context_parts = []
        
        if "user_background" in cultural_data:
            context_parts.append(f"User cultural background: {cultural_data['user_background']}")
        
        if "dietary_restrictions" in cultural_data:
            context_parts.append(f"Dietary considerations: {cultural_data['dietary_restrictions']}")
        
        if "religious_considerations" in cultural_data:
            context_parts.append(f"Religious considerations: {cultural_data['religious_considerations']}")
        
        return "CULTURAL CONTEXT:\n" + "\n".join(context_parts) if context_parts else ""
    
    def _enhance_budget_context(self, budget_data: Dict[str, Any]) -> str:
        """Enhance budget and financial context"""
        if not budget_data:
            return ""
        
        context_parts = []
        
        if "total_budget" in budget_data:
            context_parts.append(f"Total budget: {budget_data['total_budget']}")
        
        if "budget_breakdown" in budget_data:
            breakdown = budget_data["budget_breakdown"]
            context_parts.append(f"Budget priorities: {json.dumps(breakdown)}")
        
        if "currency_preference" in budget_data:
            context_parts.append(f"Preferred currency: {budget_data['currency_preference']}")
        
        return "BUDGET CONTEXT:\n" + "\n".join(context_parts) if context_parts else ""
    
    def _enhance_user_profile_context(self, profile_data: Dict[str, Any]) -> str:
        """Enhance user profile and preferences context"""
        if not profile_data:
            return ""
        
        context_parts = []
        
        if "travel_style" in profile_data:
            context_parts.append(f"Travel style: {profile_data['travel_style']}")
        
        if "interests" in profile_data:
            interests = ", ".join(profile_data["interests"])
            context_parts.append(f"Interests: {interests}")
        
        if "accessibility_needs" in profile_data:
            context_parts.append(f"Accessibility needs: {profile_data['accessibility_needs']}")
        
        return "USER PROFILE:\n" + "\n".join(context_parts) if context_parts else ""
    
    def _format_user_preferences(self, preferences: Dict[str, Any]) -> str:
        """Format user preferences for prompt inclusion"""
        formatted_prefs = []
        
        for key, value in preferences.items():
            if value:
                formatted_prefs.append(f"• {key.replace('_', ' ').title()}: {value}")
        
        return "\n".join(formatted_prefs) if formatted_prefs else "No specific preferences provided"
    
    def _extract_template_variables(self, context: Dict[str, Any]) -> Dict[str, str]:
        """Extract variables needed for template formatting"""
        variables = {
            "destinations": context.get("destinations", "Not specified"),
            "duration": context.get("duration", "Not specified"),
            "budget": context.get("budget", "Not specified"),
            "travel_style": context.get("travel_style", "Not specified"),
            "interests": context.get("interests", "Not specified"),
            "special_requirements": context.get("special_requirements", "None"),
            "location": context.get("location", "Not specified"),
            "specific_interest": context.get("specific_interest", "General information"),
            "travel_context": context.get("travel_context", "General travel"),
            "image_description": context.get("image_description", "No image provided"),
            "user_context": context.get("user_context", "No additional context"),
            "cultural_context": context.get("cultural_context", "General"),
            "situation": context.get("situation", "General inquiry"),
            "user_background": context.get("user_background", "Not specified"),
            "context": context.get("general_context", "No additional context")
        }
        
        return variables
    
    def _generate_fallback_prompt(self, user_message: str) -> Dict[str, Any]:
        """Generate a fallback prompt when no specific template is available"""
        return {
            "system_prompt": "You are SynTour AI, a helpful travel assistant for Malaysia tourism.",
            "user_prompt": f"User Query: {user_message}",
            "parameters": {"temperature": 0.7, "max_tokens": 1000, "top_p": 0.9},
            "examples": [],
            "prompt_type": "fallback"
        }
