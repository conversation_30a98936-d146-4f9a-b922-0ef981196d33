# app/core/user_agent.py
import os
import platform
from typing import Dict, Optional
from app.core.env_config import EnvironmentConfig

class UserAgentManager:
    """Centralized User-Agent string management"""
    
    # Application information
    APP_NAME = "SynTour"
    APP_VERSION = "2.0.0"
    
    @classmethod
    def get_base_user_agent(cls) -> str:
        """Get base User-Agent string with system information"""
        python_version = platform.python_version()
        system_info = f"{platform.system()}/{platform.release()}"
        
        return f"{cls.APP_NAME}/{cls.APP_VERSION} (Python/{python_version}; {system_info})"
    
    @classmethod
    def get_api_user_agent(cls, api_name: str, additional_info: Optional[str] = None) -> str:
        """
        Get User-Agent string for specific API
        
        Args:
            api_name: Name of the API service
            additional_info: Additional information to include
            
        Returns:
            Formatted User-Agent string
        """
        # Check for custom User-Agent from environment
        env_var = f"{api_name.upper()}_USER_AGENT"
        custom_ua = EnvironmentConfig.get_env_var(env_var)
        
        if custom_ua:
            return custom_ua
        
        # Build standard User-Agent
        base_ua = cls.get_base_user_agent()
        
        # Add API-specific information
        api_info = f"API/{api_name}"
        
        if additional_info:
            api_info += f" ({additional_info})"
        
        return f"{base_ua} {api_info}"
    
    @classmethod
    def get_http_headers(cls, api_name: str, additional_headers: Optional[Dict[str, str]] = None) -> Dict[str, str]:
        """
        Get standard HTTP headers including User-Agent
        
        Args:
            api_name: Name of the API service
            additional_headers: Additional headers to include
            
        Returns:
            Dictionary of HTTP headers
        """
        headers = {
            'User-Agent': cls.get_api_user_agent(api_name),
            'Accept': 'application/json',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
        }
        
        # Add custom headers if provided
        if additional_headers:
            headers.update(additional_headers)
        
        return headers
    
    @classmethod
    def get_api_specific_headers(cls, api_name: str) -> Dict[str, str]:
        """Get API-specific headers with optimized User-Agent"""
        
        api_specific_info = {
            'google_places': 'Places/Search',
            'amadeus': 'Travel/Booking',
            'flightapi': 'Flight/Search',
            'openweather': 'Weather/Forecast',
            'tomorrow_io': 'Weather/Analytics',
            'hotelbeds': 'Hotel/Booking',
            'geoapify': 'Geocoding/Maps',
        }
        
        additional_info = api_specific_info.get(api_name.lower())
        
        return cls.get_http_headers(api_name, additional_headers={
            'X-API-Client': cls.APP_NAME,
            'X-API-Version': cls.APP_VERSION,
        })
    
    @classmethod
    def update_app_info(cls, name: str, version: str) -> None:
        """Update application information"""
        cls.APP_NAME = name
        cls.APP_VERSION = version
    
    @classmethod
    def get_user_agent_info(cls) -> Dict[str, str]:
        """Get information about User-Agent configuration"""
        return {
            'app_name': cls.APP_NAME,
            'app_version': cls.APP_VERSION,
            'base_user_agent': cls.get_base_user_agent(),
            'python_version': platform.python_version(),
            'system': platform.system(),
            'platform': platform.platform(),
        }

# Convenience functions for common APIs
def get_google_places_headers(api_key: str) -> Dict[str, str]:
    """Get headers for Google Places API"""
    headers = UserAgentManager.get_api_specific_headers('google_places')
    headers['X-Goog-Api-Key'] = api_key
    return headers

def get_amadeus_headers(access_token: str) -> Dict[str, str]:
    """Get headers for Amadeus API"""
    headers = UserAgentManager.get_api_specific_headers('amadeus')
    headers['Authorization'] = f'Bearer {access_token}'
    return headers

def get_openweather_headers() -> Dict[str, str]:
    """Get headers for OpenWeather API"""
    return UserAgentManager.get_api_specific_headers('openweather')

def get_flightapi_headers() -> Dict[str, str]:
    """Get headers for FlightAPI"""
    return UserAgentManager.get_api_specific_headers('flightapi')

def get_tomorrow_io_headers(api_key: str) -> Dict[str, str]:
    """Get headers for Tomorrow.io API"""
    headers = UserAgentManager.get_api_specific_headers('tomorrow_io')
    headers['apikey'] = api_key
    return headers

def get_hotelbeds_headers(api_key: str, secret: str) -> Dict[str, str]:
    """Get headers for Hotelbeds API"""
    import hashlib
    import time
    
    headers = UserAgentManager.get_api_specific_headers('hotelbeds')
    
    # Hotelbeds requires specific signature
    timestamp = str(int(time.time()))
    signature = hashlib.sha256((api_key + secret + timestamp).encode()).hexdigest()
    
    headers.update({
        'Api-key': api_key,
        'X-Signature': signature,
        'Accept': 'application/json',
        'Accept-Encoding': 'gzip',
    })
    
    return headers

def get_geoapify_headers() -> Dict[str, str]:
    """Get headers for Geoapify API"""
    return UserAgentManager.get_api_specific_headers('geoapify')

# User-Agent validation and compliance
def validate_user_agent(user_agent: str) -> bool:
    """Validate User-Agent string format"""
    # Basic validation - should contain app name and version
    required_parts = [UserAgentManager.APP_NAME, UserAgentManager.APP_VERSION]
    return all(part in user_agent for part in required_parts)

def get_compliant_user_agent(api_name: str, requirements: Optional[Dict[str, str]] = None) -> str:
    """
    Get User-Agent string that complies with specific API requirements
    
    Args:
        api_name: Name of the API
        requirements: Specific requirements (e.g., {'max_length': 100})
        
    Returns:
        Compliant User-Agent string
    """
    user_agent = UserAgentManager.get_api_user_agent(api_name)
    
    if requirements:
        max_length = requirements.get('max_length')
        if max_length and len(user_agent) > max_length:
            # Truncate while keeping essential information
            base = f"{UserAgentManager.APP_NAME}/{UserAgentManager.APP_VERSION}"
            if len(base) <= max_length:
                return base
            else:
                return user_agent[:max_length-3] + "..."
    
    return user_agent