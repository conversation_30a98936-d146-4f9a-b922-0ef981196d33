"""
Advanced Debugging and Monitoring Tools for SynTour
Provides comprehensive system monitoring, performance tracking, and debugging utilities
"""

import time
import psutil
import asyncio
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
from dataclasses import dataclass, asdict
import logging
from contextlib import asynccontextmanager
import tracemalloc
import gc

from .logging_config import get_logger, log_performance

logger = get_logger('debug_monitor')

@dataclass
class PerformanceMetrics:
    """Performance metrics data structure"""
    operation: str
    start_time: float
    end_time: float
    duration: float
    memory_before: int
    memory_after: int
    memory_delta: int
    cpu_percent: float
    request_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None

@dataclass
class SystemHealth:
    """System health status data structure"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available: int
    disk_usage_percent: float
    active_connections: int
    error_rate: float
    response_time_avg: float

class PerformanceTracker:
    """
    Advanced performance tracking with memory and CPU monitoring
    Provides detailed insights into system performance
    """
    
    def __init__(self):
        self.metrics_history: List[PerformanceMetrics] = []
        self.max_history_size = 10000
        self.slow_operation_threshold = 1.0  # seconds
        self.memory_leak_threshold = 50 * 1024 * 1024  # 50MB
        
        # Start memory tracing in debug mode
        if logger.isEnabledFor(logging.DEBUG):
            tracemalloc.start()
    
    def performance_monitor(
        self,
        operation_name: Optional[str] = None,
        log_slow_operations: bool = True,
        memory_tracking: bool = True
    ):
        """
        Decorator for monitoring function performance
        Tracks execution time, memory usage, and CPU utilization
        """
        def decorator(func: Callable):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                return await self._track_async_performance(
                    func, operation_name or func.__name__,
                    log_slow_operations, memory_tracking,
                    *args, **kwargs
                )
            
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                return self._track_sync_performance(
                    func, operation_name or func.__name__,
                    log_slow_operations, memory_tracking,
                    *args, **kwargs
                )
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
        
        return decorator
    
    async def _track_async_performance(
        self,
        func: Callable,
        operation: str,
        log_slow: bool,
        track_memory: bool,
        *args,
        **kwargs
    ):
        """Track performance of async function"""
        
        # Get initial metrics
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss if track_memory else 0
        cpu_before = psutil.cpu_percent()
        
        request_id = getattr(kwargs.get('request', None), 'state', {}).get('request_id', None)
        
        try:
            # Execute function
            result = await func(*args, **kwargs)
            
            # Calculate metrics
            end_time = time.time()
            duration = end_time - start_time
            memory_after = psutil.Process().memory_info().rss if track_memory else 0
            memory_delta = memory_after - memory_before
            cpu_after = psutil.cpu_percent()
            cpu_avg = (cpu_before + cpu_after) / 2
            
            # Create metrics record
            metrics = PerformanceMetrics(
                operation=operation,
                start_time=start_time,
                end_time=end_time,
                duration=duration,
                memory_before=memory_before,
                memory_after=memory_after,
                memory_delta=memory_delta,
                cpu_percent=cpu_avg,
                request_id=request_id
            )
            
            # Store metrics
            self._store_metrics(metrics)
            
            # Log performance
            log_performance(logger, operation, duration, {
                'memory_delta_mb': round(memory_delta / 1024 / 1024, 2),
                'cpu_percent': round(cpu_avg, 1),
                'request_id': request_id
            })
            
            # Check for slow operations
            if log_slow and duration > self.slow_operation_threshold:
                logger.warning(
                    f"Slow operation detected: {operation} took {duration:.3f}s",
                    extra={
                        'extra_context': asdict(metrics),
                        'request_id': request_id
                    }
                )
            
            # Check for memory leaks
            if track_memory and memory_delta > self.memory_leak_threshold:
                logger.warning(
                    f"Potential memory leak: {operation} increased memory by {memory_delta / 1024 / 1024:.2f}MB",
                    extra={
                        'extra_context': asdict(metrics),
                        'request_id': request_id
                    }
                )
            
            return result
            
        except Exception as e:
            # Log error with performance context
            end_time = time.time()
            duration = end_time - start_time
            
            logger.error(
                f"Operation {operation} failed after {duration:.3f}s: {str(e)}",
                extra={
                    'extra_context': {
                        'operation': operation,
                        'duration': duration,
                        'error': str(e),
                        'request_id': request_id
                    },
                    'request_id': request_id
                },
                exc_info=True
            )
            raise
    
    def _track_sync_performance(
        self,
        func: Callable,
        operation: str,
        log_slow: bool,
        track_memory: bool,
        *args,
        **kwargs
    ):
        """Track performance of sync function"""
        
        start_time = time.time()
        memory_before = psutil.Process().memory_info().rss if track_memory else 0
        
        try:
            result = func(*args, **kwargs)
            
            end_time = time.time()
            duration = end_time - start_time
            memory_after = psutil.Process().memory_info().rss if track_memory else 0
            memory_delta = memory_after - memory_before
            
            # Log performance
            log_performance(logger, operation, duration, {
                'memory_delta_mb': round(memory_delta / 1024 / 1024, 2)
            })
            
            return result
            
        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time
            
            logger.error(
                f"Operation {operation} failed after {duration:.3f}s: {str(e)}",
                exc_info=True
            )
            raise
    
    def _store_metrics(self, metrics: PerformanceMetrics):
        """Store performance metrics with size limit"""
        self.metrics_history.append(metrics)
        
        # Maintain history size limit
        if len(self.metrics_history) > self.max_history_size:
            self.metrics_history = self.metrics_history[-self.max_history_size//2:]
    
    def get_performance_summary(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance summary for the last N hours"""
        
        cutoff_time = time.time() - (hours * 3600)
        recent_metrics = [
            m for m in self.metrics_history
            if m.start_time >= cutoff_time
        ]
        
        if not recent_metrics:
            return {"message": "No metrics available for the specified period"}
        
        # Calculate statistics
        durations = [m.duration for m in recent_metrics]
        memory_deltas = [m.memory_delta for m in recent_metrics]
        cpu_values = [m.cpu_percent for m in recent_metrics]
        
        return {
            "period_hours": hours,
            "total_operations": len(recent_metrics),
            "performance": {
                "avg_duration": sum(durations) / len(durations),
                "max_duration": max(durations),
                "min_duration": min(durations),
                "slow_operations": len([d for d in durations if d > self.slow_operation_threshold])
            },
            "memory": {
                "avg_delta_mb": sum(memory_deltas) / len(memory_deltas) / 1024 / 1024,
                "max_delta_mb": max(memory_deltas) / 1024 / 1024,
                "potential_leaks": len([d for d in memory_deltas if d > self.memory_leak_threshold])
            },
            "cpu": {
                "avg_percent": sum(cpu_values) / len(cpu_values),
                "max_percent": max(cpu_values)
            }
        }

class SystemMonitor:
    """
    System-wide monitoring for health checks and alerting
    Tracks system resources and application health
    """
    
    def __init__(self):
        self.health_history: List[SystemHealth] = []
        self.alert_thresholds = {
            'cpu_percent': 80.0,
            'memory_percent': 85.0,
            'disk_usage_percent': 90.0,
            'error_rate': 5.0,  # percentage
            'response_time_avg': 5.0  # seconds
        }
        self.monitoring_active = False
        self.monitoring_task: Optional[asyncio.Task] = None
    
    async def start_monitoring(self, interval_seconds: int = 60):
        """Start continuous system monitoring"""
        if self.monitoring_active:
            return
        
        self.monitoring_active = True
        self.monitoring_task = asyncio.create_task(
            self._monitoring_loop(interval_seconds)
        )
        logger.info("System monitoring started")
    
    async def stop_monitoring(self):
        """Stop system monitoring"""
        self.monitoring_active = False
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("System monitoring stopped")
    
    async def _monitoring_loop(self, interval: int):
        """Main monitoring loop"""
        while self.monitoring_active:
            try:
                health = await self.collect_health_metrics()
                self.health_history.append(health)
                
                # Keep only last 24 hours of data
                cutoff_time = datetime.now() - timedelta(hours=24)
                self.health_history = [
                    h for h in self.health_history
                    if h.timestamp > cutoff_time
                ]
                
                # Check for alerts
                await self._check_health_alerts(health)
                
                await asyncio.sleep(interval)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}", exc_info=True)
                await asyncio.sleep(interval)
    
    async def collect_health_metrics(self) -> SystemHealth:
        """Collect current system health metrics"""
        
        # CPU and memory
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Network connections (approximate)
        connections = len(psutil.net_connections())
        
        # Application metrics (would be integrated with actual metrics)
        error_rate = 0.0  # Placeholder
        response_time_avg = 0.0  # Placeholder
        
        return SystemHealth(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_available=memory.available,
            disk_usage_percent=disk.percent,
            active_connections=connections,
            error_rate=error_rate,
            response_time_avg=response_time_avg
        )
    
    async def _check_health_alerts(self, health: SystemHealth):
        """Check health metrics against thresholds and alert if needed"""
        
        alerts = []
        
        if health.cpu_percent > self.alert_thresholds['cpu_percent']:
            alerts.append(f"High CPU usage: {health.cpu_percent:.1f}%")
        
        if health.memory_percent > self.alert_thresholds['memory_percent']:
            alerts.append(f"High memory usage: {health.memory_percent:.1f}%")
        
        if health.disk_usage_percent > self.alert_thresholds['disk_usage_percent']:
            alerts.append(f"High disk usage: {health.disk_usage_percent:.1f}%")
        
        if alerts:
            logger.warning(
                f"System health alerts: {'; '.join(alerts)}",
                extra={
                    'extra_context': asdict(health)
                }
            )
    
    def get_current_health(self) -> Dict[str, Any]:
        """Get current system health status"""
        if not self.health_history:
            return {"status": "no_data", "message": "No health data available"}
        
        latest = self.health_history[-1]
        
        # Determine overall status
        status = "healthy"
        if (latest.cpu_percent > self.alert_thresholds['cpu_percent'] or
            latest.memory_percent > self.alert_thresholds['memory_percent']):
            status = "warning"
        
        if (latest.cpu_percent > 95 or latest.memory_percent > 95):
            status = "critical"
        
        return {
            "status": status,
            "timestamp": latest.timestamp.isoformat(),
            "metrics": asdict(latest),
            "thresholds": self.alert_thresholds
        }

class DebugMiddleware:
    """
    Debugging middleware for request tracing and performance monitoring
    Provides detailed request/response logging and performance tracking
    """
    
    def __init__(self, performance_tracker: PerformanceTracker):
        self.performance_tracker = performance_tracker
        self.request_logger = get_logger('api.requests')
    
    async def __call__(self, request, call_next):
        """Process request with debugging and monitoring"""
        
        # Generate request ID
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Log request
        start_time = time.time()
        
        self.request_logger.info(
            f"Request started: {request.method} {request.url.path}",
            extra={
                'extra_context': {
                    'method': request.method,
                    'path': str(request.url.path),
                    'query_params': dict(request.query_params),
                    'headers': dict(request.headers),
                    'client_ip': request.client.host if request.client else None
                },
                'request_id': request_id
            }
        )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Log response
            duration = time.time() - start_time
            
            self.request_logger.info(
                f"Request completed: {request.method} {request.url.path} - {response.status_code}",
                extra={
                    'extra_context': {
                        'method': request.method,
                        'path': str(request.url.path),
                        'status_code': response.status_code,
                        'duration_ms': round(duration * 1000, 2)
                    },
                    'request_id': request_id
                }
            )
            
            return response
            
        except Exception as e:
            # Log error
            duration = time.time() - start_time
            
            self.request_logger.error(
                f"Request failed: {request.method} {request.url.path} - {str(e)}",
                extra={
                    'extra_context': {
                        'method': request.method,
                        'path': str(request.url.path),
                        'error': str(e),
                        'duration_ms': round(duration * 1000, 2)
                    },
                    'request_id': request_id
                },
                exc_info=True
            )
            raise

# Global instances
performance_tracker = PerformanceTracker()
system_monitor = SystemMonitor()

# Utility functions
def get_memory_usage() -> Dict[str, Any]:
    """Get current memory usage information"""
    process = psutil.Process()
    memory_info = process.memory_info()
    
    return {
        "rss_mb": round(memory_info.rss / 1024 / 1024, 2),
        "vms_mb": round(memory_info.vms / 1024 / 1024, 2),
        "percent": round(process.memory_percent(), 2),
        "available_mb": round(psutil.virtual_memory().available / 1024 / 1024, 2)
    }

def force_garbage_collection() -> Dict[str, Any]:
    """Force garbage collection and return statistics"""
    before_objects = len(gc.get_objects())
    collected = gc.collect()
    after_objects = len(gc.get_objects())
    
    return {
        "objects_before": before_objects,
        "objects_after": after_objects,
        "objects_collected": collected,
        "objects_freed": before_objects - after_objects
    }
