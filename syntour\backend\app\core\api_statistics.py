# app/core/api_statistics.py
import asyncio
import time
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field, asdict
from datetime import datetime, timedelta
from collections import defaultdict, deque
import json
import logging

logger = logging.getLogger(__name__)

@dataclass
class APICallRecord:
    """Record of a single API call"""
    api_name: str
    endpoint: str
    method: str
    timestamp: datetime
    duration: float
    success: bool
    status_code: Optional[int] = None
    error_message: Optional[str] = None
    request_size: Optional[int] = None
    response_size: Optional[int] = None
    user_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data

@dataclass
class APIStatistics:
    """Statistics for a specific API"""
    api_name: str
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    success_rate: float = 0.0
    average_response_time: float = 0.0
    min_response_time: float = float('inf')
    max_response_time: float = 0.0
    total_response_time: float = 0.0
    calls_per_hour: float = 0.0
    error_rate: float = 0.0
    last_call_time: Optional[datetime] = None
    first_call_time: Optional[datetime] = None
    
    def update_with_call(self, call_record: APICallRecord):
        """Update statistics with new call record"""
        self.total_calls += 1
        
        if call_record.success:
            self.successful_calls += 1
        else:
            self.failed_calls += 1
        
        # Update response time statistics
        self.total_response_time += call_record.duration
        self.min_response_time = min(self.min_response_time, call_record.duration)
        self.max_response_time = max(self.max_response_time, call_record.duration)
        self.average_response_time = self.total_response_time / self.total_calls
        
        # Update rates
        self.success_rate = (self.successful_calls / self.total_calls) * 100
        self.error_rate = (self.failed_calls / self.total_calls) * 100
        
        # Update timestamps
        if self.first_call_time is None:
            self.first_call_time = call_record.timestamp
        self.last_call_time = call_record.timestamp
        
        # Calculate calls per hour
        if self.first_call_time and self.last_call_time:
            time_diff = (self.last_call_time - self.first_call_time).total_seconds() / 3600
            if time_diff > 0:
                self.calls_per_hour = self.total_calls / time_diff

class APIUsageTracker:
    """Tracks and analyzes API usage patterns"""
    
    def __init__(self, max_records: int = 10000):
        self.max_records = max_records
        self.call_records: deque = deque(maxlen=max_records)
        self.api_statistics: Dict[str, APIStatistics] = defaultdict(lambda: APIStatistics(""))
        self.hourly_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.daily_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.error_patterns: Dict[str, List[str]] = defaultdict(list)
        self.performance_alerts: List[Dict[str, Any]] = []
    
    def record_api_call(
        self,
        api_name: str,
        endpoint: str,
        method: str,
        duration: float,
        success: bool,
        status_code: Optional[int] = None,
        error_message: Optional[str] = None,
        request_size: Optional[int] = None,
        response_size: Optional[int] = None,
        user_id: Optional[str] = None
    ):
        """Record a new API call"""
        
        call_record = APICallRecord(
            api_name=api_name,
            endpoint=endpoint,
            method=method,
            timestamp=datetime.utcnow(),
            duration=duration,
            success=success,
            status_code=status_code,
            error_message=error_message,
            request_size=request_size,
            response_size=response_size,
            user_id=user_id
        )
        
        # Add to records
        self.call_records.append(call_record)
        
        # Update API statistics
        if api_name not in self.api_statistics:
            self.api_statistics[api_name] = APIStatistics(api_name=api_name)
        
        self.api_statistics[api_name].update_with_call(call_record)
        
        # Update hourly and daily statistics
        hour_key = call_record.timestamp.strftime('%Y-%m-%d-%H')
        day_key = call_record.timestamp.strftime('%Y-%m-%d')
        
        self.hourly_stats[hour_key][api_name] += 1
        self.daily_stats[day_key][api_name] += 1
        
        # Track error patterns
        if not success and error_message:
            self.error_patterns[api_name].append(error_message)
            # Keep only last 100 errors per API
            if len(self.error_patterns[api_name]) > 100:
                self.error_patterns[api_name] = self.error_patterns[api_name][-100:]
        
        # Check for performance alerts
        self._check_performance_alerts(call_record)
    
    def _check_performance_alerts(self, call_record: APICallRecord):
        """Check if call triggers any performance alerts"""
        alerts = []
        
        # Slow response alert
        if call_record.duration > 10.0:  # 10 seconds
            alerts.append({
                'type': 'SLOW_RESPONSE',
                'api_name': call_record.api_name,
                'duration': call_record.duration,
                'threshold': 10.0,
                'timestamp': call_record.timestamp.isoformat()
            })
        
        # Error rate alert
        stats = self.api_statistics[call_record.api_name]
        if stats.total_calls >= 10 and stats.error_rate > 50:  # More than 50% error rate
            alerts.append({
                'type': 'HIGH_ERROR_RATE',
                'api_name': call_record.api_name,
                'error_rate': stats.error_rate,
                'threshold': 50.0,
                'timestamp': call_record.timestamp.isoformat()
            })
        
        # Add alerts to list (keep only last 100)
        self.performance_alerts.extend(alerts)
        if len(self.performance_alerts) > 100:
            self.performance_alerts = self.performance_alerts[-100:]
    
    def get_api_statistics(self, api_name: Optional[str] = None) -> Dict[str, Any]:
        """Get statistics for specific API or all APIs"""
        if api_name:
            stats = self.api_statistics.get(api_name)
            return asdict(stats) if stats else {}
        
        return {name: asdict(stats) for name, stats in self.api_statistics.items()}
    
    def get_usage_summary(self) -> Dict[str, Any]:
        """Get comprehensive usage summary"""
        total_calls = sum(stats.total_calls for stats in self.api_statistics.values())
        total_successful = sum(stats.successful_calls for stats in self.api_statistics.values())
        total_failed = sum(stats.failed_calls for stats in self.api_statistics.values())
        
        overall_success_rate = (total_successful / total_calls * 100) if total_calls > 0 else 0
        
        # Get top APIs by usage
        top_apis = sorted(
            self.api_statistics.items(),
            key=lambda x: x[1].total_calls,
            reverse=True
        )[:5]
        
        # Get recent activity (last 24 hours)
        now = datetime.utcnow()
        recent_calls = [
            record for record in self.call_records
            if (now - record.timestamp).total_seconds() < 86400  # 24 hours
        ]
        
        return {
            'overview': {
                'total_calls': total_calls,
                'successful_calls': total_successful,
                'failed_calls': total_failed,
                'overall_success_rate': overall_success_rate,
                'apis_used': len(self.api_statistics),
                'tracking_period': self._get_tracking_period()
            },
            'top_apis': [
                {
                    'name': name,
                    'calls': stats.total_calls,
                    'success_rate': stats.success_rate,
                    'avg_response_time': stats.average_response_time
                }
                for name, stats in top_apis
            ],
            'recent_activity': {
                'last_24h_calls': len(recent_calls),
                'last_24h_success_rate': (
                    sum(1 for r in recent_calls if r.success) / len(recent_calls) * 100
                ) if recent_calls else 0
            },
            'performance_alerts': self.performance_alerts[-10:],  # Last 10 alerts
            'timestamp': datetime.utcnow().isoformat()
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics across all APIs"""
        metrics = {
            'response_times': {},
            'throughput': {},
            'reliability': {}
        }
        
        for api_name, stats in self.api_statistics.items():
            metrics['response_times'][api_name] = {
                'average': stats.average_response_time,
                'min': stats.min_response_time if stats.min_response_time != float('inf') else 0,
                'max': stats.max_response_time
            }
            
            metrics['throughput'][api_name] = {
                'calls_per_hour': stats.calls_per_hour,
                'total_calls': stats.total_calls
            }
            
            metrics['reliability'][api_name] = {
                'success_rate': stats.success_rate,
                'error_rate': stats.error_rate,
                'uptime_percentage': stats.success_rate  # Simplified uptime calculation
            }
        
        return metrics
    
    def _get_tracking_period(self) -> Dict[str, str]:
        """Get the period for which we have tracking data"""
        if not self.call_records:
            return {'start': 'N/A', 'end': 'N/A'}
        
        oldest = min(record.timestamp for record in self.call_records)
        newest = max(record.timestamp for record in self.call_records)
        
        return {
            'start': oldest.isoformat(),
            'end': newest.isoformat()
        }

# Global API usage tracker
api_usage_tracker = APIUsageTracker()

# Decorator for automatic API call tracking
def track_api_call(api_name: str, endpoint: str = ""):
    """Decorator to automatically track API calls"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            error_message = None
            status_code = None
            
            try:
                result = await func(*args, **kwargs)
                success = True
                
                # Try to extract status code from result
                if hasattr(result, 'status_code'):
                    status_code = result.status_code
                elif isinstance(result, dict) and 'status_code' in result:
                    status_code = result['status_code']
                
                return result
                
            except Exception as e:
                error_message = str(e)
                raise
            
            finally:
                duration = time.time() - start_time
                api_usage_tracker.record_api_call(
                    api_name=api_name,
                    endpoint=endpoint or func.__name__,
                    method='GET',  # Default method
                    duration=duration,
                    success=success,
                    status_code=status_code,
                    error_message=error_message
                )
        
        def sync_wrapper(*args, **kwargs):
            start_time = time.time()
            success = False
            error_message = None
            status_code = None
            
            try:
                result = func(*args, **kwargs)
                success = True
                
                if hasattr(result, 'status_code'):
                    status_code = result.status_code
                elif isinstance(result, dict) and 'status_code' in result:
                    status_code = result['status_code']
                
                return result
                
            except Exception as e:
                error_message = str(e)
                raise
            
            finally:
                duration = time.time() - start_time
                api_usage_tracker.record_api_call(
                    api_name=api_name,
                    endpoint=endpoint or func.__name__,
                    method='GET',
                    duration=duration,
                    success=success,
                    status_code=status_code,
                    error_message=error_message
                )
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator

# Convenience functions
def get_api_usage_summary() -> Dict[str, Any]:
    """Get overall API usage summary"""
    return api_usage_tracker.get_usage_summary()

def get_api_performance_report() -> Dict[str, Any]:
    """Get comprehensive performance report"""
    return {
        'statistics': api_usage_tracker.get_api_statistics(),
        'performance': api_usage_tracker.get_performance_metrics(),
        'recent_usage': api_usage_tracker.get_usage_summary()
    }