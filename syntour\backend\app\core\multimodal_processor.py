"""
Advanced Multimodal Processing System for SynTour AI
Handles image analysis, file processing, and integrated multimodal insights
"""

from typing import Dict, Any, List, Optional, Union
import base64
from PIL import Image
import io
import logging
import asyncio
from datetime import datetime

logger = logging.getLogger(__name__)

class MultimodalProcessor:
    """
    Advanced multimodal processor for handling images, files, and integrated analysis
    Provides travel-specific insights from visual and document content
    """
    
    def __init__(self):
        self.image_analyzers = {
            "landmark_detection": self._detect_landmarks,
            "cultural_analysis": self._analyze_cultural_elements,
            "safety_assessment": self._assess_safety,
            "composition_analysis": self._analyze_composition,
            "travel_relevance": self._assess_travel_relevance_detailed
        }
        
        self.file_processors = {
            "pdf": self._process_pdf,
            "image": self._process_image,
            "text": self._process_text_file,
            "document": self._process_document
        }
        
        logger.info("Multimodal Processor initialized with travel-specific analyzers")
    
    async def process_multimodal_input(
        self,
        text_input: str,
        images: List[bytes] = None,
        files: List[Dict[str, Any]] = None,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Process multimodal input combining text, images, and files
        
        Args:
            text_input: User's text message
            images: List of image data in bytes
            files: List of file information dictionaries
            context: Additional context information
            
        Returns:
            Comprehensive analysis results with integrated insights
        """
        try:
            analysis_results = {
                "text_analysis": await self._analyze_text_intent(text_input),
                "visual_analysis": {},
                "file_analysis": {},
                "integrated_insights": {},
                "processing_metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "processed_items": 0,
                    "success_rate": 1.0
                }
            }
            
            processed_count = 0
            total_items = (len(images) if images else 0) + (len(files) if files else 0)
            
            # Process images
            if images:
                logger.info(f"Processing {len(images)} images")
                for i, image_data in enumerate(images):
                    try:
                        image_analysis = await self._analyze_image(image_data, text_input, context)
                        analysis_results["visual_analysis"][f"image_{i}"] = image_analysis
                        processed_count += 1
                    except Exception as e:
                        logger.error(f"Error processing image {i}: {e}")
                        analysis_results["visual_analysis"][f"image_{i}"] = {
                            "error": f"Image processing failed: {str(e)}"
                        }
            
            # Process files
            if files:
                logger.info(f"Processing {len(files)} files")
                for file_info in files:
                    try:
                        file_analysis = await self._analyze_file(file_info, text_input, context)
                        analysis_results["file_analysis"][file_info.get("name", "unknown")] = file_analysis
                        processed_count += 1
                    except Exception as e:
                        logger.error(f"Error processing file {file_info.get('name', 'unknown')}: {e}")
                        analysis_results["file_analysis"][file_info.get("name", "unknown")] = {
                            "error": f"File processing failed: {str(e)}"
                        }
            
            # Calculate success rate
            if total_items > 0:
                analysis_results["processing_metadata"]["success_rate"] = processed_count / total_items
            
            analysis_results["processing_metadata"]["processed_items"] = processed_count
            
            # Generate integrated insights
            analysis_results["integrated_insights"] = await self._integrate_multimodal_insights(
                analysis_results, text_input, context
            )
            
            logger.info(f"Multimodal processing completed: {processed_count}/{total_items} items processed")
            return analysis_results
            
        except Exception as e:
            logger.error(f"Multimodal processing error: {e}")
            return {
                "error": f"Multimodal processing failed: {str(e)}",
                "text_analysis": await self._analyze_text_intent(text_input),
                "processing_metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "processed_items": 0,
                    "success_rate": 0.0
                }
            }
    
    async def _analyze_text_intent(self, text: str) -> Dict[str, Any]:
        """Analyze text input for travel-related intent and context"""
        try:
            text_lower = text.lower()
            
            # Detect travel intent categories
            intent_keywords = {
                "planning": ["plan", "itinerary", "schedule", "organize", "trip"],
                "information": ["what", "where", "when", "how", "tell me", "about"],
                "recommendation": ["recommend", "suggest", "best", "good", "should"],
                "booking": ["book", "reserve", "availability", "price", "cost"],
                "navigation": ["direction", "how to get", "transport", "travel to"],
                "cultural": ["culture", "custom", "tradition", "etiquette", "respect"]
            }
            
            detected_intents = []
            for intent, keywords in intent_keywords.items():
                if any(keyword in text_lower for keyword in keywords):
                    detected_intents.append(intent)
            
            # Extract potential destinations
            malaysia_destinations = [
                "kuala lumpur", "kl", "penang", "langkawi", "malacca", "melaka",
                "johor bahru", "kota kinabalu", "kuching", "cameron highlands",
                "genting", "putrajaya", "ipoh", "alor setar", "kuantan"
            ]
            
            mentioned_destinations = [
                dest for dest in malaysia_destinations 
                if dest in text_lower
            ]
            
            return {
                "detected_intents": detected_intents,
                "primary_intent": detected_intents[0] if detected_intents else "general",
                "mentioned_destinations": mentioned_destinations,
                "text_length": len(text),
                "complexity": "complex" if len(text.split()) > 20 else "simple",
                "contains_questions": "?" in text,
                "urgency_indicators": any(word in text_lower for word in ["urgent", "asap", "immediately", "now"])
            }
            
        except Exception as e:
            logger.error(f"Text analysis error: {e}")
            return {"error": str(e), "detected_intents": ["general"]}
    
    async def _analyze_image(
        self,
        image_data: bytes,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Comprehensive image analysis for travel-related content
        
        Args:
            image_data: Raw image bytes
            context: Text context from user
            additional_context: Additional context information
            
        Returns:
            Detailed image analysis results
        """
        try:
            # Basic image processing
            image = Image.open(io.BytesIO(image_data))
            image_info = {
                "size": image.size,
                "format": image.format,
                "mode": image.mode,
                "file_size": len(image_data)
            }
            
            # Run multiple analysis passes
            analysis_results = {}
            
            for analyzer_name, analyzer_func in self.image_analyzers.items():
                try:
                    result = await analyzer_func(image, context, additional_context)
                    analysis_results[analyzer_name] = result
                except Exception as e:
                    logger.warning(f"Analyzer {analyzer_name} failed: {e}")
                    analysis_results[analyzer_name] = {"error": str(e)}
            
            # Generate image description for AI processing
            image_description = self._generate_image_description(image, analysis_results)
            
            return {
                "image_info": image_info,
                "analysis": analysis_results,
                "ai_description": image_description,
                "travel_relevance_score": self._calculate_travel_relevance_score(analysis_results),
                "processing_status": "success"
            }
            
        except Exception as e:
            logger.error(f"Image analysis error: {e}")
            return {
                "error": f"Image processing failed: {str(e)}",
                "processing_status": "failed"
            }
    
    async def _detect_landmarks(
        self,
        image: Image.Image,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Detect Malaysian landmarks and tourist attractions in images"""
        
        # Malaysia landmark database (in production, this would use ML models)
        malaysia_landmarks = {
            "petronas_towers": {
                "name": "Petronas Twin Towers",
                "location": "Kuala Lumpur",
                "description": "Iconic twin skyscrapers and symbol of Malaysia",
                "visit_tips": ["Best viewed at night", "Visit Skybridge and observation deck", "KLCC Park for photos"]
            },
            "batu_caves": {
                "name": "Batu Caves",
                "location": "Selangor",
                "description": "Hindu temple complex in limestone caves",
                "visit_tips": ["Dress modestly", "Climb 272 steps", "Watch out for monkeys"]
            },
            "penang_hill": {
                "name": "Penang Hill",
                "location": "Penang",
                "description": "Historic hill station with panoramic views",
                "visit_tips": ["Take the funicular railway", "Visit during sunset", "Bring warm clothes"]
            },
            "mount_kinabalu": {
                "name": "Mount Kinabalu",
                "location": "Sabah",
                "description": "Highest mountain in Malaysia",
                "visit_tips": ["Book climbing permits in advance", "Physical fitness required", "Best season: March-August"]
            }
        }
        
        # Simulate landmark detection (in production, use computer vision API)
        detected_landmarks = []
        confidence_scores = []
        
        # Basic heuristic detection based on context
        context_lower = context.lower()
        for landmark_key, landmark_info in malaysia_landmarks.items():
            if (landmark_info["name"].lower() in context_lower or 
                landmark_info["location"].lower() in context_lower):
                detected_landmarks.append(landmark_info)
                confidence_scores.append(0.8)  # Simulated confidence
        
        return {
            "landmarks": detected_landmarks,
            "confidence_scores": confidence_scores,
            "location_suggestions": self._suggest_related_locations(detected_landmarks),
            "detection_method": "context_based"  # In production: "ml_model"
        }
    
    async def _analyze_cultural_elements(
        self,
        image: Image.Image,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze cultural elements visible in images"""
        
        cultural_indicators = {
            "architecture": ["mosque", "temple", "colonial", "traditional", "modern"],
            "food": ["street food", "restaurant", "local cuisine", "halal", "vegetarian"],
            "clothing": ["traditional dress", "modest clothing", "cultural attire"],
            "activities": ["festival", "ceremony", "market", "cultural event"],
            "symbols": ["religious symbols", "cultural artifacts", "traditional crafts"]
        }
        
        # Analyze context for cultural elements
        context_lower = context.lower()
        detected_elements = {}
        
        for category, indicators in cultural_indicators.items():
            found_indicators = [ind for ind in indicators if ind in context_lower]
            if found_indicators:
                detected_elements[category] = found_indicators
        
        # Generate cultural guidance
        cultural_tips = []
        if "mosque" in context_lower or "temple" in context_lower:
            cultural_tips.extend([
                "Dress modestly when visiting religious sites",
                "Remove shoes before entering",
                "Respect prayer times and ceremonies",
                "Ask permission before photographing people"
            ])
        
        if "food" in context_lower or "restaurant" in context_lower:
            cultural_tips.extend([
                "Try local specialties for authentic experience",
                "Look for halal certification if needed",
                "Use right hand for eating in traditional settings",
                "Tipping is not mandatory but appreciated"
            ])
        
        return {
            "detected_elements": detected_elements,
            "cultural_tips": cultural_tips,
            "sensitivity_level": "high" if detected_elements else "medium",
            "recommended_behavior": self._generate_behavior_recommendations(detected_elements)
        }
    
    async def _assess_safety(
        self,
        image: Image.Image,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Assess safety considerations from visual content"""
        
        safety_keywords = {
            "high_risk": ["cliff", "steep", "dangerous", "warning", "restricted"],
            "medium_risk": ["crowded", "busy", "traffic", "construction", "night"],
            "low_risk": ["park", "museum", "mall", "hotel", "restaurant"]
        }
        
        context_lower = context.lower()
        risk_level = "low"
        safety_concerns = []
        recommendations = []
        
        # Assess risk level based on context
        for risk, keywords in safety_keywords.items():
            if any(keyword in context_lower for keyword in keywords):
                if risk == "high_risk":
                    risk_level = "high"
                    safety_concerns.extend(["Potential physical danger", "Requires caution"])
                    recommendations.extend([
                        "Follow all safety guidelines",
                        "Consider guided tours",
                        "Inform others of your plans"
                    ])
                elif risk == "medium_risk" and risk_level == "low":
                    risk_level = "medium"
                    safety_concerns.extend(["Moderate caution advised"])
                    recommendations.extend([
                        "Stay aware of surroundings",
                        "Keep valuables secure",
                        "Travel in groups when possible"
                    ])
        
        # General safety tips for Malaysia
        general_tips = [
            "Keep copies of important documents",
            "Have emergency contacts readily available",
            "Stay hydrated in tropical climate",
            "Use reputable transportation services"
        ]
        
        return {
            "risk_level": risk_level,
            "safety_concerns": safety_concerns,
            "recommendations": recommendations,
            "general_tips": general_tips,
            "emergency_info": {
                "police": "999",
                "ambulance": "999",
                "fire": "994",
                "tourist_hotline": "1-300-88-5050"
            }
        }
    
    async def _analyze_composition(
        self,
        image: Image.Image,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze image composition for photography tips"""
        
        # Basic composition analysis based on image properties
        width, height = image.size
        aspect_ratio = width / height
        
        composition_tips = []
        
        # Aspect ratio recommendations
        if aspect_ratio > 1.5:
            composition_tips.append("Wide landscape format - great for panoramic views")
        elif aspect_ratio < 0.8:
            composition_tips.append("Portrait format - ideal for architectural details")
        else:
            composition_tips.append("Square/standard format - versatile for social media")
        
        # General photography tips for Malaysia
        photography_tips = [
            "Golden hour (sunrise/sunset) provides best lighting",
            "Use polarizing filter to reduce haze in tropical climate",
            "Respect photography restrictions at religious sites",
            "Ask permission before photographing people",
            "Protect equipment from humidity and rain"
        ]
        
        return {
            "aspect_ratio": round(aspect_ratio, 2),
            "composition_tips": composition_tips,
            "photography_tips": photography_tips,
            "best_times": ["Early morning (6-8 AM)", "Late afternoon (4-6 PM)"],
            "equipment_recommendations": [
                "Wide-angle lens for landscapes",
                "Telephoto lens for wildlife",
                "Waterproof camera bag",
                "Extra batteries (drain faster in heat)"
            ]
        }
    
    async def _assess_travel_relevance_detailed(
        self,
        image: Image.Image,
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Detailed assessment of travel relevance"""
        
        travel_categories = {
            "attractions": ["landmark", "monument", "museum", "park", "temple", "mosque"],
            "accommodation": ["hotel", "resort", "hostel", "guesthouse", "homestay"],
            "transportation": ["airport", "train", "bus", "taxi", "grab", "ferry"],
            "dining": ["restaurant", "cafe", "street food", "market", "food court"],
            "shopping": ["mall", "market", "shop", "souvenir", "craft", "bazaar"],
            "nature": ["beach", "mountain", "forest", "river", "waterfall", "island"],
            "culture": ["festival", "ceremony", "traditional", "heritage", "art"]
        }
        
        context_lower = context.lower()
        relevant_categories = []
        
        for category, keywords in travel_categories.items():
            if any(keyword in context_lower for keyword in keywords):
                relevant_categories.append(category)
        
        # Calculate relevance score
        relevance_score = min(len(relevant_categories) * 0.2, 1.0)
        
        return {
            "relevance_score": relevance_score,
            "relevant_categories": relevant_categories,
            "travel_value": "high" if relevance_score > 0.6 else "medium" if relevance_score > 0.3 else "low",
            "visitor_appeal": self._assess_visitor_appeal(relevant_categories),
            "recommended_duration": self._suggest_visit_duration(relevant_categories)
        }
    
    def _suggest_related_locations(self, landmarks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Suggest related locations based on detected landmarks"""
        
        suggestions = []
        
        for landmark in landmarks:
            location = landmark.get("location", "").lower()
            
            if "kuala lumpur" in location:
                suggestions.append({
                    "name": "KLCC Area",
                    "activities": ["Shopping at Suria KLCC", "Visit Aquaria KLCC", "Walk in KLCC Park"],
                    "nearby_attractions": ["KL Tower", "Bukit Bintang", "Chinatown"],
                    "distance": "Walking distance"
                })
            elif "penang" in location:
                suggestions.append({
                    "name": "Georgetown UNESCO Site",
                    "activities": ["Street art hunting", "Heritage walk", "Food tour"],
                    "nearby_attractions": ["Clan Houses", "Pinang Peranakan Mansion", "Cheong Fatt Tze Mansion"],
                    "distance": "15-30 minutes by car"
                })
            elif "sabah" in location:
                suggestions.append({
                    "name": "Kinabalu National Park",
                    "activities": ["Nature trails", "Canopy walk", "Hot springs"],
                    "nearby_attractions": ["Poring Hot Springs", "Kundasang War Memorial", "Desa Dairy Farm"],
                    "distance": "Within the park area"
                })
        
        return suggestions
    
    def _generate_image_description(self, image: Image.Image, analysis_results: Dict[str, Any]) -> str:
        """Generate a descriptive text for AI processing"""
        
        width, height = image.size
        
        description_parts = [
            f"Image dimensions: {width}x{height} pixels",
            f"Format: {image.format}",
            f"Color mode: {image.mode}"
        ]
        
        # Add analysis insights
        if "landmark_detection" in analysis_results:
            landmarks = analysis_results["landmark_detection"].get("landmarks", [])
            if landmarks:
                landmark_names = [l.get("name", "Unknown") for l in landmarks]
                description_parts.append(f"Detected landmarks: {', '.join(landmark_names)}")
        
        if "cultural_analysis" in analysis_results:
            elements = analysis_results["cultural_analysis"].get("detected_elements", {})
            if elements:
                description_parts.append(f"Cultural elements: {', '.join(elements.keys())}")
        
        return ". ".join(description_parts)
    
    def _calculate_travel_relevance_score(self, analysis_results: Dict[str, Any]) -> float:
        """Calculate overall travel relevance score"""
        
        scores = []
        
        # Extract scores from different analyzers
        if "travel_relevance" in analysis_results:
            travel_score = analysis_results["travel_relevance"].get("relevance_score", 0)
            scores.append(travel_score)
        
        if "landmark_detection" in analysis_results:
            landmarks = analysis_results["landmark_detection"].get("landmarks", [])
            landmark_score = min(len(landmarks) * 0.3, 1.0)
            scores.append(landmark_score)
        
        if "cultural_analysis" in analysis_results:
            elements = analysis_results["cultural_analysis"].get("detected_elements", {})
            cultural_score = min(len(elements) * 0.2, 1.0)
            scores.append(cultural_score)
        
        return sum(scores) / len(scores) if scores else 0.0
    
    def _generate_behavior_recommendations(self, cultural_elements: Dict[str, Any]) -> List[str]:
        """Generate behavior recommendations based on cultural elements"""
        
        recommendations = []
        
        if "architecture" in cultural_elements:
            recommendations.append("Respect architectural heritage and follow photography guidelines")
        
        if "food" in cultural_elements:
            recommendations.append("Try local specialties and respect dining customs")
        
        if "clothing" in cultural_elements:
            recommendations.append("Dress appropriately for cultural and religious contexts")
        
        if not recommendations:
            recommendations.append("Be respectful of local customs and traditions")
        
        return recommendations
    
    def _assess_visitor_appeal(self, categories: List[str]) -> str:
        """Assess appeal level for different types of visitors"""
        
        high_appeal_categories = ["attractions", "nature", "culture"]
        medium_appeal_categories = ["dining", "shopping", "accommodation"]
        
        high_count = sum(1 for cat in categories if cat in high_appeal_categories)
        medium_count = sum(1 for cat in categories if cat in medium_appeal_categories)
        
        if high_count >= 2:
            return "high"
        elif high_count >= 1 or medium_count >= 2:
            return "medium"
        else:
            return "low"
    
    def _suggest_visit_duration(self, categories: List[str]) -> str:
        """Suggest appropriate visit duration"""
        
        if "nature" in categories or "culture" in categories:
            return "Half day to full day"
        elif "attractions" in categories:
            return "2-4 hours"
        elif "dining" in categories or "shopping" in categories:
            return "1-2 hours"
        else:
            return "30 minutes to 1 hour"
    
    async def _analyze_file(
        self,
        file_info: Dict[str, Any],
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Analyze uploaded files for travel-relevant content"""
        
        file_type = file_info.get("type", "unknown")
        file_name = file_info.get("name", "unknown")
        
        try:
            if file_type in self.file_processors:
                processor = self.file_processors[file_type]
                return await processor(file_info, context, additional_context)
            else:
                return {
                    "error": f"Unsupported file type: {file_type}",
                    "file_name": file_name,
                    "processing_status": "unsupported"
                }
        except Exception as e:
            logger.error(f"File processing error for {file_name}: {e}")
            return {
                "error": f"File processing failed: {str(e)}",
                "file_name": file_name,
                "processing_status": "failed"
            }
    
    async def _process_pdf(
        self,
        file_info: Dict[str, Any],
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process PDF files (travel documents, itineraries, etc.)"""
        
        # Placeholder for PDF processing
        return {
            "file_type": "pdf",
            "analysis": "PDF processing not yet implemented",
            "travel_relevance": "medium",
            "processing_status": "placeholder"
        }
    
    async def _process_image(
        self,
        file_info: Dict[str, Any],
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process image files"""
        
        # This would delegate to the main image analysis
        return {
            "file_type": "image",
            "analysis": "Processed via image analyzer",
            "processing_status": "delegated"
        }
    
    async def _process_text_file(
        self,
        file_info: Dict[str, Any],
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process text files (notes, lists, etc.)"""
        
        # Placeholder for text file processing
        return {
            "file_type": "text",
            "analysis": "Text file processing not yet implemented",
            "travel_relevance": "low",
            "processing_status": "placeholder"
        }
    
    async def _process_document(
        self,
        file_info: Dict[str, Any],
        context: str,
        additional_context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Process document files (Word, etc.)"""
        
        # Placeholder for document processing
        return {
            "file_type": "document",
            "analysis": "Document processing not yet implemented",
            "travel_relevance": "medium",
            "processing_status": "placeholder"
        }
    
    async def _integrate_multimodal_insights(
        self,
        analysis_results: Dict[str, Any],
        text_input: str,
        context: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """Integrate insights from all modalities for comprehensive understanding"""
        
        try:
            text_analysis = analysis_results.get("text_analysis", {})
            visual_analysis = analysis_results.get("visual_analysis", {})
            file_analysis = analysis_results.get("file_analysis", {})
            
            # Combine detected intents and destinations
            all_intents = text_analysis.get("detected_intents", [])
            all_destinations = text_analysis.get("mentioned_destinations", [])
            
            # Extract insights from visual analysis
            visual_insights = []
            for image_key, image_data in visual_analysis.items():
                if "analysis" in image_data:
                    landmarks = image_data["analysis"].get("landmark_detection", {}).get("landmarks", [])
                    for landmark in landmarks:
                        if landmark.get("location"):
                            all_destinations.append(landmark["location"].lower())
                        visual_insights.append(f"Detected landmark: {landmark.get('name', 'Unknown')}")
            
            # Generate integrated recommendations
            recommendations = self._generate_integrated_recommendations(
                all_intents, all_destinations, visual_insights, text_input
            )
            
            # Calculate overall confidence
            confidence_score = self._calculate_overall_confidence(analysis_results)
            
            return {
                "combined_intents": list(set(all_intents)),
                "combined_destinations": list(set(all_destinations)),
                "visual_insights": visual_insights,
                "integrated_recommendations": recommendations,
                "confidence_score": confidence_score,
                "processing_summary": {
                    "text_processed": bool(text_analysis),
                    "images_processed": len(visual_analysis),
                    "files_processed": len(file_analysis),
                    "total_insights": len(visual_insights) + len(all_intents)
                }
            }
            
        except Exception as e:
            logger.error(f"Integration error: {e}")
            return {
                "error": f"Failed to integrate insights: {str(e)}",
                "confidence_score": 0.0
            }
    
    def _generate_integrated_recommendations(
        self,
        intents: List[str],
        destinations: List[str],
        visual_insights: List[str],
        text_input: str
    ) -> List[str]:
        """Generate recommendations based on integrated analysis"""
        
        recommendations = []
        
        # Intent-based recommendations
        if "planning" in intents:
            recommendations.append("Consider creating a detailed itinerary with time allocations")
        
        if "information" in intents:
            recommendations.append("Gather comprehensive information about opening hours and entry fees")
        
        if "cultural" in intents:
            recommendations.append("Research local customs and appropriate behavior guidelines")
        
        # Destination-based recommendations
        if destinations:
            unique_destinations = list(set(destinations))
            if len(unique_destinations) > 1:
                recommendations.append("Plan efficient transportation between multiple destinations")
            
            for dest in unique_destinations[:3]:  # Limit to top 3
                recommendations.append(f"Explore local specialties and hidden gems in {dest.title()}")
        
        # Visual insight-based recommendations
        if visual_insights:
            recommendations.append("Use visual references to enhance your travel experience")
            if any("landmark" in insight.lower() for insight in visual_insights):
                recommendations.append("Visit during optimal lighting conditions for best photography")
        
        # Default recommendations
        if not recommendations:
            recommendations.extend([
                "Consider your budget and time constraints when planning",
                "Check weather conditions and seasonal factors",
                "Book accommodations and transportation in advance"
            ])
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _calculate_overall_confidence(self, analysis_results: Dict[str, Any]) -> float:
        """Calculate overall confidence score for the analysis"""
        
        confidence_scores = []
        
        # Text analysis confidence
        text_analysis = analysis_results.get("text_analysis", {})
        if text_analysis and "error" not in text_analysis:
            confidence_scores.append(0.8)
        
        # Visual analysis confidence
        visual_analysis = analysis_results.get("visual_analysis", {})
        for image_data in visual_analysis.values():
            if "travel_relevance_score" in image_data:
                confidence_scores.append(image_data["travel_relevance_score"])
        
        # Processing success rate
        metadata = analysis_results.get("processing_metadata", {})
        success_rate = metadata.get("success_rate", 0.0)
        confidence_scores.append(success_rate)
        
        return sum(confidence_scores) / len(confidence_scores) if confidence_scores else 0.0
