from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date, time
from enum import Enum

# Enums for FlightAPI constants
class CabinClass(str, Enum):
    ECONOMY = "Economy"
    PREMIUM_ECONOMY = "Premium Economy"
    BUSINESS = "Business"
    FIRST = "First"

class TripType(str, Enum):
    ONEWAY = "oneway"
    ROUNDTRIP = "roundtrip"

class SortBy(str, Enum):
    PRICE = "price"
    DURATION = "duration"
    DEPARTURE_TIME = "departure_time"
    ARRIVAL_TIME = "arrival_time"

class FlightStatus(str, Enum):
    SCHEDULED = "scheduled"
    ACTIVE = "active"
    LANDED = "landed"
    CANCELLED = "cancelled"
    INCIDENT = "incident"
    DIVERTED = "diverted"

# Base models for common structures
class Airport(BaseModel):
    iata: str
    icao: Optional[str] = None
    name: Optional[str] = None
    city: Optional[str] = None
    country: Optional[str] = None
    timezone: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None

class Airline(BaseModel):
    iata: str
    icao: Optional[str] = None
    name: Optional[str] = None
    logo: Optional[str] = None

class Aircraft(BaseModel):
    model: Optional[str] = None
    code: Optional[str] = None

# Flight segment models
class FlightTime(BaseModel):
    scheduled: Optional[str] = None  # ISO datetime string
    estimated: Optional[str] = None  # ISO datetime string
    actual: Optional[str] = None     # ISO datetime string
    runway: Optional[str] = None     # ISO datetime string

class FlightEndpoint(BaseModel):
    airport: Airport
    terminal: Optional[str] = None
    gate: Optional[str] = None
    baggage: Optional[str] = None
    delay: Optional[int] = None  # Delay in minutes
    time: FlightTime

class FlightSegment(BaseModel):
    departure: FlightEndpoint
    arrival: FlightEndpoint
    airline: Airline
    aircraft: Optional[Aircraft] = None
    flight_number: str
    duration: Optional[str] = None  # Duration in format like "2h 30m"
    distance: Optional[int] = None  # Distance in kilometers
    cabin_class: Optional[CabinClass] = None

# Price models
class PriceBreakdown(BaseModel):
    base_fare: Optional[float] = None
    taxes: Optional[float] = None
    fees: Optional[float] = None
    total: float
    currency: str = "USD"

class Price(BaseModel):
    adult: Optional[PriceBreakdown] = None
    child: Optional[PriceBreakdown] = None
    infant: Optional[PriceBreakdown] = None
    total: PriceBreakdown
    per_passenger: Optional[PriceBreakdown] = None

# Baggage models
class BaggageAllowance(BaseModel):
    carry_on: Optional[Dict[str, Any]] = None
    checked: Optional[Dict[str, Any]] = None
    personal_item: Optional[Dict[str, Any]] = None

# Flight offer models
class FlightItinerary(BaseModel):
    segments: List[FlightSegment]
    duration: Optional[str] = None  # Total journey duration
    stops: int = 0
    layovers: Optional[List[Dict[str, Any]]] = None

class FlightOffer(BaseModel):
    id: Optional[str] = None
    price: Price
    itineraries: List[FlightItinerary]  # Outbound and return for round-trip
    validating_airline: Optional[Airline] = None
    baggage: Optional[BaggageAllowance] = None
    refundable: Optional[bool] = None
    changeable: Optional[bool] = None
    last_ticketing_date: Optional[str] = None
    booking_class: Optional[str] = None
    fare_basis: Optional[str] = None

# Response models for different endpoints
class OnewayTripResponse(BaseModel):
    success: bool
    data: Optional[List[FlightOffer]] = None
    message: Optional[str] = None
    total_results: Optional[int] = None
    search_id: Optional[str] = None
    currency: Optional[str] = "USD"
    search_params: Optional[Dict[str, Any]] = None

class RoundTripResponse(BaseModel):
    success: bool
    data: Optional[List[FlightOffer]] = None
    message: Optional[str] = None
    total_results: Optional[int] = None
    search_id: Optional[str] = None
    currency: Optional[str] = "USD"
    search_params: Optional[Dict[str, Any]] = None

# Airport Schedule models
class ScheduledFlight(BaseModel):
    flight_number: str
    airline: Airline
    aircraft: Optional[Aircraft] = None
    departure: FlightEndpoint
    arrival: FlightEndpoint
    status: FlightStatus
    duration: Optional[str] = None
    distance: Optional[int] = None
    route: Optional[List[str]] = None  # List of airport codes in route

class AirportSchedule(BaseModel):
    airport: Airport
    date: str  # YYYY-MM-DD format
    departures: Optional[List[ScheduledFlight]] = None
    arrivals: Optional[List[ScheduledFlight]] = None
    total_departures: Optional[int] = None
    total_arrivals: Optional[int] = None

class AirportScheduleResponse(BaseModel):
    success: bool
    data: Optional[AirportSchedule] = None
    message: Optional[str] = None
    search_params: Optional[Dict[str, Any]] = None

# Request models
class OnewayTripRequest(BaseModel):
    """Request model for one-way trip search"""
    origin: str = Field(..., description="Origin airport IATA code")
    destination: str = Field(..., description="Destination airport IATA code")
    departure_date: str = Field(..., description="Departure date in YYYY-MM-DD format")
    adults: int = Field(1, ge=1, le=9, description="Number of adult passengers")
    children: Optional[int] = Field(None, ge=0, le=9, description="Number of child passengers")
    infants: Optional[int] = Field(None, ge=0, le=9, description="Number of infant passengers")
    cabin_class: Optional[CabinClass] = Field(None, description="Preferred cabin class")
    currency: Optional[str] = Field("USD", description="Currency for prices")
    max_results: Optional[int] = Field(50, ge=1, le=100, description="Maximum number of results")
    sort_by: Optional[SortBy] = Field(SortBy.PRICE, description="Sort results by")
    max_stops: Optional[int] = Field(None, ge=0, le=3, description="Maximum number of stops")
    airlines: Optional[List[str]] = Field(None, description="Preferred airline codes")
    exclude_airlines: Optional[List[str]] = Field(None, description="Airlines to exclude")
    max_price: Optional[float] = Field(None, ge=0, description="Maximum price filter")
    departure_time_from: Optional[str] = Field(None, description="Earliest departure time (HH:MM)")
    departure_time_to: Optional[str] = Field(None, description="Latest departure time (HH:MM)")

class RoundTripRequest(BaseModel):
    """Request model for round-trip search"""
    origin: str = Field(..., description="Origin airport IATA code")
    destination: str = Field(..., description="Destination airport IATA code")
    departure_date: str = Field(..., description="Departure date in YYYY-MM-DD format")
    return_date: str = Field(..., description="Return date in YYYY-MM-DD format")
    adults: int = Field(1, ge=1, le=9, description="Number of adult passengers")
    children: Optional[int] = Field(None, ge=0, le=9, description="Number of child passengers")
    infants: Optional[int] = Field(None, ge=0, le=9, description="Number of infant passengers")
    cabin_class: Optional[CabinClass] = Field(None, description="Preferred cabin class")
    currency: Optional[str] = Field("USD", description="Currency for prices")
    max_results: Optional[int] = Field(50, ge=1, le=100, description="Maximum number of results")
    sort_by: Optional[SortBy] = Field(SortBy.PRICE, description="Sort results by")
    max_stops: Optional[int] = Field(None, ge=0, le=3, description="Maximum number of stops")
    airlines: Optional[List[str]] = Field(None, description="Preferred airline codes")
    exclude_airlines: Optional[List[str]] = Field(None, description="Airlines to exclude")
    max_price: Optional[float] = Field(None, ge=0, description="Maximum price filter")
    departure_time_from: Optional[str] = Field(None, description="Earliest departure time (HH:MM)")
    departure_time_to: Optional[str] = Field(None, description="Latest departure time (HH:MM)")
    return_time_from: Optional[str] = Field(None, description="Earliest return time (HH:MM)")
    return_time_to: Optional[str] = Field(None, description="Latest return time (HH:MM)")

class AirportScheduleRequest(BaseModel):
    """Request model for airport schedule"""
    airport: str = Field(..., description="Airport IATA code")
    date: str = Field(..., description="Date in YYYY-MM-DD format")
    type: Optional[str] = Field("both", description="Schedule type: 'departures', 'arrivals', or 'both'")
    limit: Optional[int] = Field(100, ge=1, le=500, description="Maximum number of flights")
    airline: Optional[str] = Field(None, description="Filter by airline IATA code")
    flight_number: Optional[str] = Field(None, description="Filter by specific flight number")

# Error models
class FlightAPIError(BaseModel):
    code: str
    message: str
    details: Optional[Dict[str, Any]] = None

class FlightAPIErrorResponse(BaseModel):
    success: bool = False
    error: FlightAPIError
    timestamp: Optional[str] = None

# Utility models for search parameters
class FlightSearchFilters(BaseModel):
    """Common filters for flight searches"""
    max_price: Optional[float] = None
    max_stops: Optional[int] = None
    airlines: Optional[List[str]] = None
    exclude_airlines: Optional[List[str]] = None
    departure_time_from: Optional[str] = None
    departure_time_to: Optional[str] = None
    arrival_time_from: Optional[str] = None
    arrival_time_to: Optional[str] = None
    max_duration: Optional[str] = None
    aircraft_types: Optional[List[str]] = None

class PassengerCount(BaseModel):
    """Passenger count model"""
    adults: int = Field(1, ge=1, le=9)
    children: Optional[int] = Field(None, ge=0, le=9)
    infants: Optional[int] = Field(None, ge=0, le=9)
    
    @property
    def total_passengers(self) -> int:
        """Calculate total number of passengers"""
        total = self.adults
        if self.children:
            total += self.children
        if self.infants:
            total += self.infants
        return total

# Statistics and analytics models
class SearchStatistics(BaseModel):
    """Statistics for flight search results"""
    total_results: int
    average_price: Optional[float] = None
    min_price: Optional[float] = None
    max_price: Optional[float] = None
    average_duration: Optional[str] = None
    airlines_count: Optional[int] = None
    direct_flights: Optional[int] = None
    connecting_flights: Optional[int] = None

class FlightSearchResponse(BaseModel):
    """Generic flight search response with statistics"""
    success: bool
    data: Optional[List[FlightOffer]] = None
    statistics: Optional[SearchStatistics] = None
    message: Optional[str] = None
    search_id: Optional[str] = None
    currency: str = "USD"
    search_params: Optional[Dict[str, Any]] = None
    timestamp: Optional[str] = None

# Booking-related models (for future extension)
class BookingContact(BaseModel):
    """Contact information for booking"""
    email: str
    phone: str
    first_name: str
    last_name: str

class PassengerInfo(BaseModel):
    """Passenger information for booking"""
    type: str  # "adult", "child", "infant"
    first_name: str
    last_name: str
    date_of_birth: str  # YYYY-MM-DD
    gender: Optional[str] = None
    passport_number: Optional[str] = None
    passport_expiry: Optional[str] = None
    nationality: Optional[str] = None

class BookingRequest(BaseModel):
    """Booking request model (for future implementation)"""
    offer_id: str
    contact: BookingContact
    passengers: List[PassengerInfo]
    payment_method: Optional[str] = None
    special_requests: Optional[List[str]] = None