# app/middleware/rate_limiter.py
import time
import asyncio
from typing import Dict, Optional
from fastapi import Request, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
from collections import defaultdict, deque
import logging

logger = logging.getLogger(__name__)

class RateLimiter:
    """Token bucket rate limiter"""
    
    def __init__(self, max_requests: int, time_window: int = 60):
        self.max_requests = max_requests
        self.time_window = time_window
        self.requests = defaultdict(deque)
        self.lock = asyncio.Lock()
    
    async def is_allowed(self, key: str) -> bool:
        """Check if request is allowed under rate limit"""
        async with self.lock:
            now = time.time()
            
            # Clean old requests outside time window
            while (self.requests[key] and 
                   now - self.requests[key][0] > self.time_window):
                self.requests[key].popleft()
            
            # Check if under limit
            if len(self.requests[key]) < self.max_requests:
                self.requests[key].append(now)
                return True
            
            return False
    
    async def get_reset_time(self, key: str) -> Optional[float]:
        """Get time when rate limit resets"""
        async with self.lock:
            if not self.requests[key]:
                return None
            
            oldest_request = self.requests[key][0]
            return oldest_request + self.time_window

class RateLimitMiddleware(BaseHTTPMiddleware):
    """Rate limiting middleware for API endpoints"""
    
    def __init__(self, app, default_rate_limit: int = 100):
        super().__init__(app)
        self.default_rate_limit = default_rate_limit
        
        # Different rate limits for different API endpoints
        self.rate_limiters = {
            "/api/amadeus": RateLimiter(50, 60),      # 50 requests per minute
            "/api/hotelbeds": RateLimiter(100, 60),   # 100 requests per minute
            "/api/geoapify": RateLimiter(1000, 60),   # 1000 requests per minute
            "/api/googleplaces": RateLimiter(100, 60), # 100 requests per minute
            "/api/openweather": RateLimiter(60, 60),   # 60 requests per minute
            "/api/tomorrow": RateLimiter(100, 60),     # 100 requests per minute
            "/api/flightapi": RateLimiter(50, 60),     # 50 requests per minute
        }
        
        # Default rate limiter
        self.default_limiter = RateLimiter(default_rate_limit, 60)
    
    def get_client_id(self, request: Request) -> str:
        """Get client identifier for rate limiting"""
        # Try to get user ID from authentication
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            return f"user:{user_id}"
        
        # Fall back to IP address
        forwarded_for = request.headers.get('X-Forwarded-For')
        if forwarded_for:
            client_ip = forwarded_for.split(',')[0].strip()
        else:
            client_ip = request.client.host if request.client else 'unknown'
        
        return f"ip:{client_ip}"
    
    def get_rate_limiter(self, path: str) -> RateLimiter:
        """Get appropriate rate limiter for path"""
        for prefix, limiter in self.rate_limiters.items():
            if path.startswith(prefix):
                return limiter
        return self.default_limiter
    
    async def dispatch(self, request: Request, call_next):
        """Process request with rate limiting"""
        
        # Skip rate limiting for health checks and static files
        if (request.url.path.startswith('/health') or 
            request.url.path.startswith('/static') or
            request.url.path.startswith('/docs') or
            request.url.path.startswith('/openapi')):
            return await call_next(request)
        
        client_id = self.get_client_id(request)
        rate_limiter = self.get_rate_limiter(request.url.path)
        
        # Check rate limit
        if not await rate_limiter.is_allowed(client_id):
            reset_time = await rate_limiter.get_reset_time(client_id)
            
            logger.warning(f"Rate limit exceeded for {client_id} on {request.url.path}")
            
            headers = {
                "X-RateLimit-Limit": str(rate_limiter.max_requests),
                "X-RateLimit-Remaining": "0",
                "X-RateLimit-Reset": str(int(reset_time)) if reset_time else str(int(time.time() + 60))
            }
            
            raise HTTPException(
                status_code=429,
                detail="Rate limit exceeded. Please try again later.",
                headers=headers
            )
        
        # Add rate limit headers to response
        response = await call_next(request)
        
        # Calculate remaining requests (approximate)
        remaining = max(0, rate_limiter.max_requests - len(rate_limiter.requests[client_id]))
        reset_time = await rate_limiter.get_reset_time(client_id)
        
        response.headers["X-RateLimit-Limit"] = str(rate_limiter.max_requests)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(reset_time)) if reset_time else str(int(time.time() + 60))
        
        return response