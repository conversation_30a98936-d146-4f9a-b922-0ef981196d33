# app/core/logging_config.py
import logging
import logging.handlers
import sys
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path
from .api_config import APIConfig

class StructuredFormatter(logging.Formatter):
    """
    Custom formatter for structured logging with JSON output
    Provides consistent log format with context information
    """

    def format(self, record: logging.LogRecord) -> str:
        # Create base log entry
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno
        }

        # Add exception information if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info)
            }

        # Add extra context if present
        if hasattr(record, 'extra_context'):
            log_entry["context"] = record.extra_context

        # Add request ID if present
        if hasattr(record, 'request_id'):
            log_entry["request_id"] = record.request_id

        # Add performance metrics if present
        if hasattr(record, 'performance'):
            log_entry["performance"] = record.performance

        return json.dumps(log_entry, ensure_ascii=False)

class ContextFilter(logging.Filter):
    """
    Filter to add contextual information to log records
    Enhances logs with request tracking and performance data
    """

    def filter(self, record: logging.LogRecord) -> bool:
        # Add process and thread information
        record.process_id = os.getpid()
        record.thread_id = record.thread

        # Add environment information
        record.environment = APIConfig.get_environment().value

        return True

def setup_enhanced_logging() -> None:
    """
    Setup enhanced logging configuration with structured output,
    file rotation, and performance tracking
    """

    log_level = APIConfig.get_log_level()
    environment = APIConfig.get_environment()

    # Create logs directory
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Configure formatters
    structured_formatter = StructuredFormatter()

    # Simple formatter for console in development
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Create handlers
    handlers = []

    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    if environment.value == 'production':
        console_handler.setFormatter(structured_formatter)
    else:
        console_handler.setFormatter(console_formatter)
    handlers.append(console_handler)

    # File handlers with rotation
    if environment.value in ['production', 'staging']:
        # Main application log
        app_handler = logging.handlers.RotatingFileHandler(
            log_dir / "syntour_app.log",
            maxBytes=50 * 1024 * 1024,  # 50MB
            backupCount=10
        )
        app_handler.setFormatter(structured_formatter)
        handlers.append(app_handler)

        # Error log
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "syntour_errors.log",
            maxBytes=20 * 1024 * 1024,  # 20MB
            backupCount=5
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(structured_formatter)
        handlers.append(error_handler)

        # Performance log
        perf_handler = logging.handlers.RotatingFileHandler(
            log_dir / "syntour_performance.log",
            maxBytes=30 * 1024 * 1024,  # 30MB
            backupCount=7
        )
        perf_handler.setFormatter(structured_formatter)

        # Create performance logger
        perf_logger = logging.getLogger('syntour.performance')
        perf_logger.addHandler(perf_handler)
        perf_logger.setLevel(logging.INFO)
        perf_logger.propagate = False

    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level))

    # Clear existing handlers
    root_logger.handlers.clear()

    # Add new handlers
    for handler in handlers:
        handler.addFilter(ContextFilter())
        root_logger.addHandler(handler)

    # Configure specific loggers
    loggers_config = {
        'syntour': log_level,
        'syntour.ai': 'DEBUG' if environment.value == 'development' else 'INFO',
        'syntour.api': 'DEBUG' if environment.value == 'development' else 'INFO',
        'syntour.performance': 'INFO',
        'uvicorn': 'INFO',
        'uvicorn.access': 'INFO' if environment.value == 'production' else 'DEBUG',
        'aiohttp': 'WARNING',
        'httpx': 'WARNING',
        'asyncio': 'WARNING'
    }

    for logger_name, level in loggers_config.items():
        logger = logging.getLogger(logger_name)
        logger.setLevel(getattr(logging, level))

    # Disable sensitive loggers in production
    if environment.value == 'production':
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)
        logging.getLogger('google.auth').setLevel(logging.WARNING)

    # Log startup message
    logger = logging.getLogger('syntour.startup')
    logger.info(
        "Enhanced logging system initialized",
        extra={
            'extra_context': {
                'environment': environment.value,
                'log_level': log_level,
                'handlers_count': len(handlers)
            }
        }
    )

# Backward compatibility
def setup_logging() -> None:
    """Backward compatible logging setup function"""
    setup_enhanced_logging()

# Utility functions for structured logging
def get_logger(name: str) -> logging.Logger:
    """Get a logger with the syntour namespace"""
    return logging.getLogger(f'syntour.{name}')

def log_performance(
    logger: logging.Logger,
    operation: str,
    duration: float,
    context: Optional[Dict[str, Any]] = None
):
    """Log performance metrics in a structured way"""
    perf_data = {
        'operation': operation,
        'duration_ms': round(duration * 1000, 2),
        'context': context or {}
    }

    logger.info(
        f"Performance: {operation} completed in {duration:.3f}s",
        extra={'performance': perf_data}
    )

def log_api_request(
    logger: logging.Logger,
    method: str,
    path: str,
    status_code: int,
    duration: float,
    request_id: Optional[str] = None,
    user_id: Optional[str] = None
):
    """Log API request in structured format"""
    request_data = {
        'method': method,
        'path': path,
        'status_code': status_code,
        'duration_ms': round(duration * 1000, 2),
        'user_id': user_id
    }

    extra = {'extra_context': request_data}
    if request_id:
        extra['request_id'] = request_id

    logger.info(
        f"{method} {path} - {status_code} ({duration:.3f}s)",
        extra=extra
    )