# SynTour 后端数据处理优化建议

## 概述
基于对SynTour后端数据处理流程的分析，本文档提供数据验证、转换、存储、数据库操作和查询性能的全面优化建议。

## 1. 数据验证优化

### 1.1 当前验证机制分析
**现状问题:**
```python
# 当前分散的验证逻辑
def validate_date_format(date_string: str) -> bool:
    try:
        datetime.strptime(date_string, "%Y-%m-%d")
        return True
    except ValueError:
        return False

def validate_iata_code(code: str) -> bool:
    return len(code) == 3 and code.isalpha() and code.isupper()
```

**问题识别:**
- 验证逻辑分散在各个服务中
- 缺乏统一的验证框架
- 错误信息不够详细
- 没有数据清洗机制

### 1.2 统一数据验证框架
**优化建议:**
```python
# 新建 app/core/data_validator.py
from typing import Any, Dict, List, Optional, Union, Callable
from pydantic import BaseModel, validator, ValidationError
from datetime import datetime, date
import re
from enum import Enum

class ValidationSeverity(Enum):
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"

class ValidationResult:
    def __init__(self):
        self.is_valid = True
        self.errors: List[Dict[str, Any]] = []
        self.warnings: List[Dict[str, Any]] = []
        self.cleaned_data: Dict[str, Any] = {}
    
    def add_error(self, field: str, message: str, value: Any = None):
        self.is_valid = False
        self.errors.append({
            "field": field,
            "message": message,
            "value": value,
            "severity": ValidationSeverity.ERROR
        })
    
    def add_warning(self, field: str, message: str, value: Any = None):
        self.warnings.append({
            "field": field,
            "message": message,
            "value": value,
            "severity": ValidationSeverity.WARNING
        })

class DataValidator:
    def __init__(self):
        self.validators = {
            "date": self._validate_date,
            "iata_code": self._validate_iata_code,
            "coordinates": self._validate_coordinates,
            "email": self._validate_email,
            "phone": self._validate_phone,
            "currency": self._validate_currency,
            "duration": self._validate_duration
        }
        
        # 数据清洗规则
        self.cleaners = {
            "text": self._clean_text,
            "phone": self._clean_phone,
            "coordinates": self._clean_coordinates,
            "currency": self._clean_currency
        }
    
    def validate_and_clean(
        self,
        data: Dict[str, Any],
        schema: Dict[str, Dict[str, Any]]
    ) -> ValidationResult:
        """验证和清洗数据"""
        result = ValidationResult()
        
        for field_name, field_config in schema.items():
            if field_name not in data:
                if field_config.get("required", False):
                    result.add_error(field_name, "Field is required")
                continue
            
            value = data[field_name]
            field_type = field_config.get("type")
            
            # 数据清洗
            if field_type in self.cleaners:
                cleaned_value = self.cleaners[field_type](value)
                result.cleaned_data[field_name] = cleaned_value
            else:
                result.cleaned_data[field_name] = value
            
            # 数据验证
            if field_type in self.validators:
                is_valid, error_msg = self.validators[field_type](
                    result.cleaned_data[field_name],
                    field_config
                )
                if not is_valid:
                    result.add_error(field_name, error_msg, value)
        
        return result
    
    def _validate_date(self, value: Any, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证日期格式"""
        if not isinstance(value, (str, date, datetime)):
            return False, "Date must be string, date, or datetime"
        
        if isinstance(value, str):
            date_format = config.get("format", "%Y-%m-%d")
            try:
                parsed_date = datetime.strptime(value, date_format)
                
                # 检查日期范围
                min_date = config.get("min_date")
                max_date = config.get("max_date")
                
                if min_date and parsed_date.date() < min_date:
                    return False, f"Date must be after {min_date}"
                
                if max_date and parsed_date.date() > max_date:
                    return False, f"Date must be before {max_date}"
                
                return True, ""
            except ValueError:
                return False, f"Invalid date format. Expected: {date_format}"
        
        return True, ""
    
    def _validate_coordinates(self, value: Any, config: Dict[str, Any]) -> tuple[bool, str]:
        """验证坐标"""
        if isinstance(value, str):
            # 尝试解析 "lat,lon" 格式
            parts = value.split(",")
            if len(parts) != 2:
                return False, "Coordinates must be in 'latitude,longitude' format"
            
            try:
                lat, lon = float(parts[0].strip()), float(parts[1].strip())
            except ValueError:
                return False, "Coordinates must be valid numbers"
        
        elif isinstance(value, (list, tuple)) and len(value) == 2:
            try:
                lat, lon = float(value[0]), float(value[1])
            except (ValueError, TypeError):
                return False, "Coordinates must be valid numbers"
        
        else:
            return False, "Invalid coordinate format"
        
        # 验证坐标范围
        if not (-90 <= lat <= 90):
            return False, "Latitude must be between -90 and 90"
        
        if not (-180 <= lon <= 180):
            return False, "Longitude must be between -180 and 180"
        
        return True, ""
    
    def _clean_text(self, value: str) -> str:
        """清洗文本数据"""
        if not isinstance(value, str):
            return str(value)
        
        # 移除多余空格
        cleaned = re.sub(r'\s+', ' ', value.strip())
        
        # 移除特殊字符（保留基本标点）
        cleaned = re.sub(r'[^\w\s\-.,!?()[\]{}:;"\']', '', cleaned)
        
        return cleaned
    
    def _clean_phone(self, value: str) -> str:
        """清洗电话号码"""
        if not isinstance(value, str):
            return str(value)
        
        # 只保留数字、+、-、()、空格
        cleaned = re.sub(r'[^\d+\-() ]', '', value)
        
        # 标准化格式
        cleaned = re.sub(r'\s+', ' ', cleaned.strip())
        
        return cleaned
```

## 2. 数据转换优化

### 2.1 统一数据转换管道
**优化建议:**
```python
# 新建 app/core/data_transformer.py
from typing import Any, Dict, List, Callable, Optional
from abc import ABC, abstractmethod
import json
from datetime import datetime

class DataTransformer(ABC):
    @abstractmethod
    def transform(self, data: Any) -> Any:
        pass

class APIResponseTransformer(DataTransformer):
    """API响应数据转换器"""
    
    def __init__(self):
        self.transformers = {
            "amadeus": self._transform_amadeus_response,
            "hotelbeds": self._transform_hotelbeds_response,
            "geoapify": self._transform_geoapify_response,
            "google_places": self._transform_google_places_response,
            "tomorrow_io": self._transform_tomorrow_io_response,
            "flight_api": self._transform_flight_api_response
        }
    
    def transform(self, api_name: str, data: Any) -> Dict[str, Any]:
        """转换API响应数据为统一格式"""
        transformer = self.transformers.get(api_name)
        if not transformer:
            return {"raw_data": data, "transformed": False}
        
        try:
            transformed_data = transformer(data)
            return {
                "data": transformed_data,
                "transformed": True,
                "api_source": api_name,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "raw_data": data,
                "transformed": False,
                "error": str(e),
                "api_source": api_name
            }
    
    def _transform_amadeus_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换Amadeus API响应"""
        if "data" in data:
            flights = []
            for flight_offer in data["data"]:
                transformed_flight = {
                    "id": flight_offer.get("id"),
                    "price": {
                        "total": float(flight_offer.get("price", {}).get("total", 0)),
                        "currency": flight_offer.get("price", {}).get("currency", "USD")
                    },
                    "itineraries": self._transform_amadeus_itineraries(
                        flight_offer.get("itineraries", [])
                    ),
                    "traveler_pricings": flight_offer.get("travelerPricings", []),
                    "source": "amadeus"
                }
                flights.append(transformed_flight)
            
            return {
                "flights": flights,
                "meta": data.get("meta", {}),
                "dictionaries": data.get("dictionaries", {})
            }
        
        return data
    
    def _transform_hotelbeds_response(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """转换Hotelbeds API响应"""
        if "hotels" in data:
            hotels = []
            for hotel_data in data["hotels"]:
                transformed_hotel = {
                    "id": hotel_data.get("code"),
                    "name": hotel_data.get("name", {}).get("content", ""),
                    "location": {
                        "coordinates": {
                            "latitude": hotel_data.get("coordinates", {}).get("latitude"),
                            "longitude": hotel_data.get("coordinates", {}).get("longitude")
                        },
                        "address": hotel_data.get("address", {}).get("content", ""),
                        "city": hotel_data.get("city", {}).get("content", ""),
                        "country": hotel_data.get("countryCode", "")
                    },
                    "category": hotel_data.get("categoryCode"),
                    "facilities": [
                        facility.get("facilityCode") 
                        for facility in hotel_data.get("facilities", [])
                    ],
                    "source": "hotelbeds"
                }
                hotels.append(transformed_hotel)
            
            return {"hotels": hotels}
        
        return data

class DataAggregator:
    """数据聚合器"""
    
    def __init__(self):
        self.aggregation_rules = {
            "flights": self._aggregate_flight_data,
            "hotels": self._aggregate_hotel_data,
            "places": self._aggregate_place_data,
            "weather": self._aggregate_weather_data
        }
    
    def aggregate(self, data_type: str, data_sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """聚合多个数据源的数据"""
        aggregator = self.aggregation_rules.get(data_type)
        if not aggregator:
            return {"error": f"No aggregator for data type: {data_type}"}
        
        return aggregator(data_sources)
    
    def _aggregate_flight_data(self, sources: List[Dict[str, Any]]) -> Dict[str, Any]:
        """聚合航班数据"""
        all_flights = []
        price_ranges = {"min": float('inf'), "max": 0}
        
        for source in sources:
            if source.get("transformed") and "data" in source:
                flights = source["data"].get("flights", [])
                all_flights.extend(flights)
                
                # 计算价格范围
                for flight in flights:
                    price = flight.get("price", {}).get("total", 0)
                    if price > 0:
                        price_ranges["min"] = min(price_ranges["min"], price)
                        price_ranges["max"] = max(price_ranges["max"], price)
        
        # 去重和排序
        unique_flights = self._deduplicate_flights(all_flights)
        sorted_flights = sorted(unique_flights, key=lambda x: x.get("price", {}).get("total", 0))
        
        return {
            "flights": sorted_flights,
            "summary": {
                "total_count": len(sorted_flights),
                "price_range": price_ranges,
                "sources": [s.get("api_source") for s in sources if s.get("transformed")]
            }
        }
    
    def _deduplicate_flights(self, flights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """去重航班数据"""
        seen = set()
        unique_flights = []
        
        for flight in flights:
            # 创建唯一标识符
            identifier = self._create_flight_identifier(flight)
            if identifier not in seen:
                seen.add(identifier)
                unique_flights.append(flight)
        
        return unique_flights
    
    def _create_flight_identifier(self, flight: Dict[str, Any]) -> str:
        """创建航班唯一标识符"""
        itineraries = flight.get("itineraries", [])
        if not itineraries:
            return str(flight.get("id", ""))
        
        # 使用第一个行程的关键信息创建标识符
        first_itinerary = itineraries[0]
        segments = first_itinerary.get("segments", [])
        
        if segments:
            first_segment = segments[0]
            departure = first_segment.get("departure", {})
            arrival = first_segment.get("arrival", {})
            
            identifier = f"{departure.get('iataCode', '')}_{arrival.get('iataCode', '')}_{departure.get('at', '')}"
            return identifier
        
        return str(flight.get("id", ""))
```

## 3. 数据存储优化

### 3.1 分层存储策略
**优化建议:**
```python
# 新建 app/core/storage_manager.py
from typing import Any, Dict, Optional, List
from enum import Enum
import json
import asyncio
from datetime import datetime, timedelta

class StorageLayer(Enum):
    CACHE = "cache"          # 内存缓存
    FAST_STORAGE = "fast"    # Redis/快速存储
    PERSISTENT = "persistent" # 数据库持久化
    ARCHIVE = "archive"      # 归档存储

class DataCategory(Enum):
    USER_SESSION = "user_session"
    API_RESPONSE = "api_response"
    TRAVEL_PLAN = "travel_plan"
    USER_PREFERENCE = "user_preference"
    ANALYTICS = "analytics"

class StorageManager:
    def __init__(self):
        self.storage_rules = {
            DataCategory.USER_SESSION: {
                "primary": StorageLayer.FAST_STORAGE,
                "backup": StorageLayer.PERSISTENT,
                "ttl": 7200  # 2小时
            },
            DataCategory.API_RESPONSE: {
                "primary": StorageLayer.CACHE,
                "backup": StorageLayer.FAST_STORAGE,
                "ttl": 1800  # 30分钟
            },
            DataCategory.TRAVEL_PLAN: {
                "primary": StorageLayer.PERSISTENT,
                "backup": StorageLayer.ARCHIVE,
                "ttl": None  # 永久保存
            },
            DataCategory.USER_PREFERENCE: {
                "primary": StorageLayer.PERSISTENT,
                "backup": None,
                "ttl": None
            },
            DataCategory.ANALYTICS: {
                "primary": StorageLayer.FAST_STORAGE,
                "backup": StorageLayer.ARCHIVE,
                "ttl": 86400  # 24小时
            }
        }
        
        self.storage_backends = {}
    
    def register_backend(self, layer: StorageLayer, backend):
        """注册存储后端"""
        self.storage_backends[layer] = backend
    
    async def store(
        self,
        category: DataCategory,
        key: str,
        data: Any,
        custom_ttl: Optional[int] = None
    ) -> bool:
        """存储数据"""
        rules = self.storage_rules.get(category)
        if not rules:
            return False
        
        ttl = custom_ttl or rules.get("ttl")
        
        # 主存储
        primary_layer = rules["primary"]
        primary_backend = self.storage_backends.get(primary_layer)
        
        if primary_backend:
            success = await self._store_to_backend(
                primary_backend, key, data, ttl
            )
            
            if success:
                # 备份存储
                backup_layer = rules.get("backup")
                if backup_layer:
                    backup_backend = self.storage_backends.get(backup_layer)
                    if backup_backend:
                        await self._store_to_backend(
                            backup_backend, key, data, ttl
                        )
                
                return True
        
        return False
    
    async def retrieve(
        self,
        category: DataCategory,
        key: str
    ) -> Optional[Any]:
        """检索数据"""
        rules = self.storage_rules.get(category)
        if not rules:
            return None
        
        # 尝试从主存储检索
        primary_layer = rules["primary"]
        primary_backend = self.storage_backends.get(primary_layer)
        
        if primary_backend:
            data = await self._retrieve_from_backend(primary_backend, key)
            if data is not None:
                return data
        
        # 尝试从备份存储检索
        backup_layer = rules.get("backup")
        if backup_layer:
            backup_backend = self.storage_backends.get(backup_layer)
            if backup_backend:
                data = await self._retrieve_from_backend(backup_backend, key)
                if data is not None:
                    # 恢复到主存储
                    if primary_backend:
                        await self._store_to_backend(
                            primary_backend, key, data, rules.get("ttl")
                        )
                    return data
        
        return None
```

## 4. 数据库操作优化

### 4.1 查询优化
**优化建议:**
```python
# 新建 app/core/database_optimizer.py
import asyncio
from typing import Dict, List, Any, Optional
import time
from dataclasses import dataclass

@dataclass
class QueryMetrics:
    query: str
    execution_time: float
    rows_affected: int
    timestamp: datetime

class DatabaseOptimizer:
    def __init__(self):
        self.query_cache = {}
        self.query_metrics: List[QueryMetrics] = []
        self.slow_query_threshold = 1.0  # 1秒
        
    async def execute_optimized_query(
        self,
        connection,
        query: str,
        params: tuple = None,
        use_cache: bool = True
    ) -> Any:
        """执行优化的查询"""
        # 生成缓存键
        cache_key = self._generate_cache_key(query, params)
        
        # 检查缓存
        if use_cache and cache_key in self.query_cache:
            cache_entry = self.query_cache[cache_key]
            if not self._is_cache_expired(cache_entry):
                return cache_entry["result"]
        
        # 执行查询并记录指标
        start_time = time.time()
        
        try:
            if params:
                result = await connection.fetch(query, *params)
            else:
                result = await connection.fetch(query)
            
            execution_time = time.time() - start_time
            
            # 记录指标
            metrics = QueryMetrics(
                query=query[:100],  # 截断长查询
                execution_time=execution_time,
                rows_affected=len(result) if result else 0,
                timestamp=datetime.now()
            )
            self.query_metrics.append(metrics)
            
            # 检查慢查询
            if execution_time > self.slow_query_threshold:
                await self._handle_slow_query(query, execution_time, params)
            
            # 缓存结果
            if use_cache:
                self.query_cache[cache_key] = {
                    "result": result,
                    "timestamp": time.time(),
                    "ttl": 300  # 5分钟
                }
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            await self._handle_query_error(query, str(e), execution_time)
            raise
    
    async def _handle_slow_query(
        self,
        query: str,
        execution_time: float,
        params: tuple = None
    ):
        """处理慢查询"""
        logger.warning(
            f"Slow query detected: {execution_time:.3f}s - {query[:100]}..."
        )
        
        # 可以在这里添加查询优化建议
        suggestions = self._analyze_query_performance(query)
        if suggestions:
            logger.info(f"Query optimization suggestions: {suggestions}")
    
    def _analyze_query_performance(self, query: str) -> List[str]:
        """分析查询性能并提供优化建议"""
        suggestions = []
        
        query_lower = query.lower()
        
        # 检查常见性能问题
        if "select *" in query_lower:
            suggestions.append("Avoid SELECT *, specify needed columns")
        
        if "order by" in query_lower and "limit" not in query_lower:
            suggestions.append("Consider adding LIMIT to ORDER BY queries")
        
        if query_lower.count("join") > 3:
            suggestions.append("Consider breaking down complex joins")
        
        if "like '%%" in query_lower:
            suggestions.append("Leading wildcard LIKE queries are slow, consider full-text search")
        
        return suggestions

# 新建 app/core/connection_pool.py
import asyncpg
from typing import Optional, Dict, Any
import asyncio

class DatabaseConnectionPool:
    def __init__(self):
        self.pool: Optional[asyncpg.Pool] = None
        self.config = {
            "min_size": 5,
            "max_size": 20,
            "command_timeout": 60,
            "server_settings": {
                "jit": "off",
                "application_name": "syntour_backend"
            }
        }
    
    async def initialize(self, database_url: str, **kwargs):
        """初始化连接池"""
        config = {**self.config, **kwargs}
        
        self.pool = await asyncpg.create_pool(
            database_url,
            **config
        )
    
    async def execute_transaction(self, operations: List[Dict[str, Any]]) -> List[Any]:
        """执行事务"""
        async with self.pool.acquire() as connection:
            async with connection.transaction():
                results = []
                for operation in operations:
                    query = operation["query"]
                    params = operation.get("params", ())
                    
                    if operation.get("type") == "fetch":
                        result = await connection.fetch(query, *params)
                    elif operation.get("type") == "execute":
                        result = await connection.execute(query, *params)
                    else:
                        result = await connection.fetchrow(query, *params)
                    
                    results.append(result)
                
                return results
    
    async def close(self):
        """关闭连接池"""
        if self.pool:
            await self.pool.close()
```

## 5. 实施优先级和时间表

### 高优先级 (立即实施)
1. **统一数据验证框架** - 1周
2. **数据转换管道** - 1周
3. **基础存储优化** - 1周

### 中优先级 (2-3周内)
1. **数据库连接池** - 1周
2. **查询优化器** - 2周
3. **数据聚合器** - 1周

### 低优先级 (长期优化)
1. **高级存储策略** - 2周
2. **性能监控** - 2周
3. **自动化优化** - 3周

## 6. 监控和指标

### 关键性能指标
- 数据验证成功率
- 数据转换处理时间
- 数据库查询响应时间
- 缓存命中率
- 存储层性能指标

## 总结

这些数据处理优化建议将显著提升SynTour系统的数据质量、处理效率和存储性能。建议按照优先级逐步实施，并持续监控关键指标以确保优化效果。
