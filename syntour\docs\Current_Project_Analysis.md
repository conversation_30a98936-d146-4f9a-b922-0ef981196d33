1. **代码调试和优化建议**：
   - 检索现有代码库，识别性能瓶颈、内存泄漏、异常处理不当等问题
   - 分析API响应时间和数据库查询效率
   - 提供具体的代码优化方案和最佳实践建议

2. **API整合优化**：
   - 评估当前已集成的API（Amadeus、Hotelbeds、Geoapify、Tomorrow.io、FlightAPI、Google Places）的使用效率
   - 建议API调用的缓存策略、错误重试机制和负载均衡
   - 优化API密钥管理和安全性配置

3. **后端Agent功能增强**：
   - 分析现有的AI Agent架构和工作流程
   - 建议改进Agent的决策逻辑、任务调度和状态管理
   - 优化Agent与各个API服务的交互模式

4. **后端数据处理优化**：
   - 检查数据验证、转换和存储流程
   - 优化数据库操作和查询性能
   - 改进异步处理和并发控制机制

5. **AI输出格式标准化**：
   - 定义统一的AI响应格式规范（JSON结构、字段命名、数据类型）
   - 建议实现响应格式的版本控制和向后兼容性
   - 优化前后端数据交互的序列化/反序列化

6. **AI输出长度控制**：
   - 分析当前AI响应的长度分布和用户体验影响
   - 建议实现动态长度控制机制（基于内容类型、用户偏好、设备类型）
   - 提供内容摘要和分页策略

7. **交互逻辑优化**：
   - 评估用户请求处理流程和响应时间
   - 改进错误处理和用户反馈机制
   - 优化会话管理和上下文保持

8. **基座大模型能力最大化**：
   - 分析当前模型的使用模式和效果
   - 建议prompt工程优化和模型微调策略
   - 探索多模态能力的集成（文本、图像、语音）

9. **API架构改进**：
   - 评估当前RESTful API设计的合理性
   - 建议API版本管理、文档生成和测试策略
   - 优化API网关、限流和监控机制

！！！暂时不要改动代码，在E:\syntour\syntour\docs下面生成有必要的md 