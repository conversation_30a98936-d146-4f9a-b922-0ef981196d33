# SynTour AI优化系统部署指南

## 概述

本指南介绍如何部署和配置SynTour AI优化系统，包括分层Prompt架构、多模态处理和性能监控功能。

## 系统要求

### 软件依赖
- Python 3.8+
- FastAPI
- Google Cloud AI Platform
- PIL (Python Imaging Library)
- asyncio

### 硬件建议
- 内存: 最少4GB，推荐8GB+
- CPU: 多核处理器，推荐4核+
- 存储: 至少10GB可用空间

## 部署步骤

### 1. 环境配置

确保以下环境变量已正确设置：

```bash
# Google Cloud配置
GOOGLE_CLOUD_PROJECT=your-project-id
GOOGLE_CLOUD_LOCATION=your-location
VERTEX_AI_ENDPOINT=your-model-endpoint

# 可选：Google Speech API
GOOGLE_SPEECH_API_KEY=your-speech-api-key
GOOGLE_APPLICATION_CREDENTIALS=path/to/credentials.json
```

### 2. 文件结构

确认以下AI优化模块文件已正确放置：

```
syntour/backend/
├── app/
│   ├── core/
│   │   ├── prompt_engineering.py      # 分层Prompt架构
│   │   ├── multimodal_processor.py    # 多模态处理
│   │   └── model_performance.py       # 性能监控
│   └── services/
│       ├── prompt_templates.py        # 现有prompt模板
│       └── file_processor.py          # 文件处理服务
└── main.py                            # 主应用文件
```

### 3. 系统初始化

AI优化系统在FastAPI启动时自动初始化：

```python
# 在startup事件中自动执行
@app.on_event("startup")
async def startup_event():
    global prompt_engine, multimodal_processor, performance_monitor
    
    # 初始化AI优化系统
    prompt_engine = AdvancedPromptEngine()
    multimodal_processor = MultimodalProcessor()
    performance_monitor = ModelPerformanceMonitor()
```

### 4. 验证部署

#### 4.1 基础功能测试

```bash
# 进入后端目录
cd syntour/backend

# 测试模块导入
python -c "
from app.core.prompt_engineering import AdvancedPromptEngine
from app.core.multimodal_processor import MultimodalProcessor
from app.core.model_performance import ModelPerformanceMonitor
print('所有模块导入成功')
"
```

#### 4.2 API端点测试

启动服务后测试以下端点：

```bash
# 健康检查 (包含AI系统状态)
curl http://localhost:8000/health

# 性能监控
curl http://localhost:8000/api/ai/performance

# 增强聊天功能
curl -X POST http://localhost:8000/api/ai/chat \
  -H "Content-Type: application/json" \
  -d '{"message": "Plan a trip to Penang"}'
```

## 配置选项

### 1. Prompt引擎配置

在`prompt_engineering.py`中可调整：

```python
# 性能阈值
self.thresholds = {
    PerformanceMetric.RESPONSE_TIME: {
        "warning": 5.0,    # 可调整
        "critical": 10.0   # 可调整
    }
}

# Prompt模板参数
parameters = {
    "temperature": 0.3,    # 可调整 (0.1-1.0)
    "max_tokens": 2000,    # 可调整
    "top_p": 0.9          # 可调整 (0.1-1.0)
}
```

### 2. 多模态处理配置

在`multimodal_processor.py`中可调整：

```python
# 地标数据库
malaysia_landmarks = {
    # 可添加更多地标信息
    "new_landmark": {
        "name": "新地标",
        "location": "位置",
        "description": "描述",
        "visit_tips": ["建议1", "建议2"]
    }
}
```

### 3. 性能监控配置

在`model_performance.py`中可调整：

```python
# 监控阈值
self.thresholds = {
    PerformanceMetric.USER_SATISFACTION: {
        "warning": 0.7,    # 70% 可调整
        "critical": 0.5    # 50% 可调整
    }
}

# 数据保留期
await self.cleanup_old_data(days=7)  # 可调整天数
```

## 监控和维护

### 1. 性能监控

通过`/api/ai/performance`端点监控：
- 响应时间趋势
- Token使用统计
- 用户满意度评分
- 错误率分析
- 缓存命中率

### 2. 日志监控

关键日志信息：
```
INFO: Advanced Prompt Engine initialized
INFO: Multimodal Processor initialized  
INFO: Model Performance Monitor initialized
WARNING: Performance Alert: Response Time is 8.5s (threshold: 5s)
```

### 3. 自动优化

系统会在以下情况自动触发优化：
- 响应时间超过阈值
- Token使用量过高
- 用户满意度下降
- 错误率上升

### 4. 手动维护

定期执行：
```python
# 清理旧数据
await performance_monitor.cleanup_old_data(days=7)

# 检查性能摘要
summary = performance_monitor.get_performance_summary(hours=24)
```

## 故障排除

### 常见问题

1. **模块导入失败**
   - 检查Python路径配置
   - 确认所有依赖已安装

2. **AI系统初始化失败**
   - 检查Google Cloud凭据
   - 验证环境变量设置

3. **性能监控无数据**
   - 确认有API调用产生数据
   - 检查时间范围设置

4. **多模态处理错误**
   - 检查文件格式支持
   - 验证图像处理库安装

### 调试模式

启用详细日志：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 升级指南

### 版本兼容性
- 当前版本: 2.1.0
- 向后兼容: 支持所有2.x版本API

### 升级步骤
1. 备份现有配置
2. 更新代码文件
3. 重启服务
4. 验证功能正常

## 支持和联系

如遇到问题，请检查：
1. 系统日志文件
2. 性能监控数据
3. API响应错误信息

技术支持：参考SynTour项目文档或联系开发团队。
