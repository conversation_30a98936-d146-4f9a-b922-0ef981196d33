# SynTour API整合优化建议

## 概述
基于对SynTour后端系统中6个主要API（Amadeus、Hotelbeds、Geoapify、Tomorrow.io、FlightAPI、Google Places）的分析，本文档提供API使用效率、缓存策略、错误重试机制和负载均衡的优化建议。

## 1. API使用效率评估

### 1.1 当前API配置分析
**现状:**
```python
# app/core/api_config.py 中的配置
API_CONFIGS = {
    "amadeus": {"rate_limit": 50, "cache_ttl": 3600},
    "hotelbeds": {"rate_limit": 100, "cache_ttl": 300},
    "geoapify": {"rate_limit": 1000, "cache_ttl": 3600},
    "google_places": {"rate_limit": 100, "cache_ttl": 3600},
    "tomorrow_io": {"rate_limit": 100, "cache_ttl": 300},
    "flight_api": {"rate_limit": 50, "cache_ttl": 60}
}
```

**问题识别:**
- 缓存TTL设置不够智能化
- 缺乏基于API响应内容的动态缓存策略
- 没有API健康状态监控

### 1.2 智能缓存策略优化

**优化建议:**
```python
# 新建 app/core/intelligent_cache.py
from enum import Enum
from typing import Dict, Any, Optional
import hashlib
import json

class CacheStrategy(Enum):
    STATIC_DATA = "static"      # 静态数据，长期缓存
    DYNAMIC_DATA = "dynamic"    # 动态数据，短期缓存
    REAL_TIME = "realtime"      # 实时数据，极短缓存
    PERSONALIZED = "personal"   # 个性化数据，用户级缓存

class IntelligentCacheManager:
    def __init__(self):
        self.cache_strategies = {
            # 航班数据 - 价格变化频繁
            "amadeus_flight_offers": CacheStrategy.DYNAMIC_DATA,
            "flightapi_search": CacheStrategy.DYNAMIC_DATA,
            
            # 酒店数据 - 相对稳定
            "hotelbeds_hotels": CacheStrategy.STATIC_DATA,
            
            # 地理位置数据 - 非常稳定
            "geoapify_places": CacheStrategy.STATIC_DATA,
            "google_places_details": CacheStrategy.STATIC_DATA,
            
            # 天气数据 - 频繁变化
            "tomorrow_io_weather": CacheStrategy.REAL_TIME,
            "openweather_current": CacheStrategy.REAL_TIME,
        }
        
        self.ttl_mapping = {
            CacheStrategy.STATIC_DATA: 86400,    # 24小时
            CacheStrategy.DYNAMIC_DATA: 1800,    # 30分钟
            CacheStrategy.REAL_TIME: 300,        # 5分钟
            CacheStrategy.PERSONALIZED: 3600     # 1小时
        }
    
    def get_cache_key(self, api_name: str, endpoint: str, params: Dict[str, Any]) -> str:
        """生成智能缓存键"""
        # 对参数进行标准化排序
        sorted_params = json.dumps(params, sort_keys=True)
        key_string = f"{api_name}:{endpoint}:{sorted_params}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def get_ttl(self, api_name: str, endpoint: str, response_data: Any = None) -> int:
        """基于内容动态计算TTL"""
        cache_key = f"{api_name}_{endpoint}"
        strategy = self.cache_strategies.get(cache_key, CacheStrategy.DYNAMIC_DATA)
        
        base_ttl = self.ttl_mapping[strategy]
        
        # 基于响应内容调整TTL
        if response_data and isinstance(response_data, dict):
            # 如果数据包含价格信息，缩短缓存时间
            if any(key in str(response_data).lower() for key in ['price', 'cost', 'fare']):
                base_ttl = min(base_ttl, 900)  # 最多15分钟
            
            # 如果数据包含实时信息，进一步缩短
            if any(key in str(response_data).lower() for key in ['current', 'now', 'live']):
                base_ttl = min(base_ttl, 300)  # 最多5分钟
        
        return base_ttl
```

## 2. 错误重试机制优化

### 2.1 智能重试策略
**当前问题:**
- 所有API使用相同的重试策略
- 没有基于错误类型的差异化处理
- 缺乏熔断机制

**优化建议:**
```python
# 新建 app/core/retry_strategy.py
import asyncio
import random
from enum import Enum
from typing import Dict, List, Callable, Any
from datetime import datetime, timedelta

class RetryStrategy(Enum):
    EXPONENTIAL_BACKOFF = "exponential"
    LINEAR_BACKOFF = "linear"
    FIXED_INTERVAL = "fixed"
    JITTERED_EXPONENTIAL = "jittered_exponential"

class CircuitBreakerState(Enum):
    CLOSED = "closed"      # 正常状态
    OPEN = "open"          # 熔断状态
    HALF_OPEN = "half_open"  # 半开状态

class APIRetryManager:
    def __init__(self):
        self.api_configs = {
            "amadeus": {
                "max_retries": 3,
                "strategy": RetryStrategy.EXPONENTIAL_BACKOFF,
                "base_delay": 1.0,
                "max_delay": 60.0,
                "retryable_status_codes": [429, 500, 502, 503, 504],
                "circuit_breaker_threshold": 5,
                "circuit_breaker_timeout": 300
            },
            "hotelbeds": {
                "max_retries": 2,
                "strategy": RetryStrategy.JITTERED_EXPONENTIAL,
                "base_delay": 2.0,
                "max_delay": 30.0,
                "retryable_status_codes": [429, 500, 502, 503],
                "circuit_breaker_threshold": 3,
                "circuit_breaker_timeout": 180
            },
            # ... 其他API配置
        }
        
        self.circuit_breakers = {}
        self.failure_counts = {}
    
    async def execute_with_retry(
        self,
        api_name: str,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """执行带重试的API调用"""
        config = self.api_configs.get(api_name, self._get_default_config())
        
        # 检查熔断器状态
        if not self._is_circuit_closed(api_name):
            raise Exception(f"Circuit breaker is open for {api_name}")
        
        last_exception = None
        
        for attempt in range(config["max_retries"] + 1):
            try:
                result = await func(*args, **kwargs)
                # 成功时重置失败计数
                self.failure_counts[api_name] = 0
                return result
                
            except Exception as e:
                last_exception = e
                
                # 记录失败
                self._record_failure(api_name)
                
                # 检查是否应该重试
                if not self._should_retry(e, config, attempt):
                    break
                
                # 计算延迟时间
                delay = self._calculate_delay(config, attempt)
                await asyncio.sleep(delay)
        
        # 所有重试都失败，抛出最后的异常
        raise last_exception
    
    def _calculate_delay(self, config: Dict, attempt: int) -> float:
        """计算重试延迟时间"""
        base_delay = config["base_delay"]
        max_delay = config["max_delay"]
        strategy = config["strategy"]
        
        if strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = base_delay * (2 ** attempt)
        elif strategy == RetryStrategy.JITTERED_EXPONENTIAL:
            delay = base_delay * (2 ** attempt) * (0.5 + random.random() * 0.5)
        elif strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = base_delay * (attempt + 1)
        else:  # FIXED_INTERVAL
            delay = base_delay
        
        return min(delay, max_delay)
```

## 3. 负载均衡和API健康监控

### 3.1 API健康监控系统
**优化建议:**
```python
# 新建 app/core/api_health_monitor.py
import asyncio
import time
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class APIHealthMetrics:
    api_name: str
    success_rate: float
    avg_response_time: float
    error_count: int
    last_success: Optional[datetime]
    last_failure: Optional[datetime]
    status: str  # "healthy", "degraded", "unhealthy"

class APIHealthMonitor:
    def __init__(self):
        self.metrics: Dict[str, APIHealthMetrics] = {}
        self.response_times: Dict[str, List[float]] = {}
        self.error_counts: Dict[str, int] = {}
        self.success_counts: Dict[str, int] = {}
        self.monitoring_window = timedelta(minutes=15)
    
    async def record_api_call(
        self,
        api_name: str,
        response_time: float,
        success: bool,
        error_type: Optional[str] = None
    ):
        """记录API调用结果"""
        now = datetime.now()
        
        # 初始化指标
        if api_name not in self.metrics:
            self.metrics[api_name] = APIHealthMetrics(
                api_name=api_name,
                success_rate=1.0 if success else 0.0,
                avg_response_time=response_time,
                error_count=0 if success else 1,
                last_success=now if success else None,
                last_failure=now if not success else None,
                status="healthy"
            )
            self.response_times[api_name] = []
            self.error_counts[api_name] = 0
            self.success_counts[api_name] = 0
        
        # 更新响应时间
        self.response_times[api_name].append(response_time)
        if len(self.response_times[api_name]) > 100:
            self.response_times[api_name] = self.response_times[api_name][-100:]
        
        # 更新计数
        if success:
            self.success_counts[api_name] += 1
            self.metrics[api_name].last_success = now
        else:
            self.error_counts[api_name] += 1
            self.metrics[api_name].last_failure = now
        
        # 更新指标
        await self._update_metrics(api_name)
    
    async def _update_metrics(self, api_name: str):
        """更新API健康指标"""
        total_calls = self.success_counts[api_name] + self.error_counts[api_name]
        
        if total_calls > 0:
            success_rate = self.success_counts[api_name] / total_calls
            self.metrics[api_name].success_rate = success_rate
        
        if self.response_times[api_name]:
            avg_response_time = sum(self.response_times[api_name]) / len(self.response_times[api_name])
            self.metrics[api_name].avg_response_time = avg_response_time
        
        self.metrics[api_name].error_count = self.error_counts[api_name]
        
        # 确定健康状态
        self.metrics[api_name].status = self._determine_health_status(api_name)
    
    def _determine_health_status(self, api_name: str) -> str:
        """确定API健康状态"""
        metrics = self.metrics[api_name]
        
        # 检查成功率
        if metrics.success_rate < 0.5:
            return "unhealthy"
        elif metrics.success_rate < 0.8:
            return "degraded"
        
        # 检查响应时间
        if metrics.avg_response_time > 10.0:  # 10秒
            return "degraded"
        elif metrics.avg_response_time > 30.0:  # 30秒
            return "unhealthy"
        
        return "healthy"
```

### 3.2 API负载均衡
**优化建议:**
```python
# 新建 app/core/api_load_balancer.py
from typing import List, Dict, Optional
import random

class APILoadBalancer:
    def __init__(self, health_monitor: APIHealthMonitor):
        self.health_monitor = health_monitor
        self.api_endpoints = {
            "amadeus": ["primary", "secondary"],
            "flight_api": ["endpoint1", "endpoint2", "endpoint3"]
        }
    
    def get_best_endpoint(self, api_name: str) -> Optional[str]:
        """选择最佳API端点"""
        endpoints = self.api_endpoints.get(api_name, [])
        if not endpoints:
            return None
        
        # 获取健康的端点
        healthy_endpoints = []
        for endpoint in endpoints:
            endpoint_key = f"{api_name}_{endpoint}"
            if endpoint_key in self.health_monitor.metrics:
                status = self.health_monitor.metrics[endpoint_key].status
                if status in ["healthy", "degraded"]:
                    healthy_endpoints.append((endpoint, status))
        
        if not healthy_endpoints:
            # 如果没有健康的端点，随机选择一个
            return random.choice(endpoints)
        
        # 优先选择健康的端点
        healthy_only = [ep for ep, status in healthy_endpoints if status == "healthy"]
        if healthy_only:
            return random.choice(healthy_only)
        
        # 如果只有降级的端点，选择其中一个
        return random.choice([ep for ep, _ in healthy_endpoints])
```

## 4. API密钥管理和安全性

### 4.1 密钥轮换机制
**优化建议:**
```python
# 新建 app/core/api_key_manager.py
import os
from typing import Dict, List, Optional
from datetime import datetime, timedelta
import asyncio

class APIKeyManager:
    def __init__(self):
        self.primary_keys: Dict[str, str] = {}
        self.backup_keys: Dict[str, List[str]] = {}
        self.key_usage_counts: Dict[str, int] = {}
        self.key_last_used: Dict[str, datetime] = {}
    
    def load_keys_from_env(self):
        """从环境变量加载API密钥"""
        api_names = ["amadeus", "hotelbeds", "geoapify", "google_places", "tomorrow_io", "flight_api"]
        
        for api_name in api_names:
            # 主密钥
            primary_key = os.getenv(f"{api_name.upper()}_API_KEY")
            if primary_key:
                self.primary_keys[api_name] = primary_key
            
            # 备用密钥
            backup_keys = []
            for i in range(1, 4):  # 最多3个备用密钥
                backup_key = os.getenv(f"{api_name.upper()}_API_KEY_BACKUP_{i}")
                if backup_key:
                    backup_keys.append(backup_key)
            
            if backup_keys:
                self.backup_keys[api_name] = backup_keys
    
    def get_active_key(self, api_name: str) -> Optional[str]:
        """获取当前活跃的API密钥"""
        # 检查主密钥是否可用
        primary_key = self.primary_keys.get(api_name)
        if primary_key and self._is_key_healthy(api_name, primary_key):
            return primary_key
        
        # 尝试备用密钥
        backup_keys = self.backup_keys.get(api_name, [])
        for backup_key in backup_keys:
            if self._is_key_healthy(api_name, backup_key):
                return backup_key
        
        # 如果没有健康的密钥，返回主密钥（让上层处理错误）
        return primary_key
    
    def _is_key_healthy(self, api_name: str, key: str) -> bool:
        """检查密钥是否健康"""
        key_id = f"{api_name}_{key[-4:]}"  # 使用密钥后4位作为标识
        
        # 检查使用频率
        usage_count = self.key_usage_counts.get(key_id, 0)
        if usage_count > 1000:  # 如果使用次数过多，可能接近限额
            return False
        
        return True
```

## 5. 实施优先级和时间表

### 高优先级 (立即实施)
1. **智能缓存策略** - 1周
   - 实施基于内容的动态TTL
   - 优化缓存键生成策略

2. **错误重试机制** - 1周
   - 实施差异化重试策略
   - 添加熔断机制

### 中优先级 (2-3周内)
1. **API健康监控** - 2周
   - 实施实时健康监控
   - 添加性能指标收集

2. **负载均衡** - 1周
   - 实施智能端点选择
   - 添加故障转移机制

### 低优先级 (长期优化)
1. **密钥管理** - 2周
   - 实施密钥轮换
   - 添加使用量监控

2. **高级缓存策略** - 3周
   - 实施分布式缓存
   - 添加缓存预热机制

## 6. 监控和指标

### 6.1 关键性能指标 (KPIs)
- API响应时间 (P50, P95, P99)
- API成功率
- 缓存命中率
- 错误重试成功率
- 熔断器触发频率

### 6.2 告警机制
```python
# 新建 app/core/alerting.py
class AlertManager:
    def __init__(self):
        self.alert_thresholds = {
            "response_time_p95": 5.0,  # 5秒
            "success_rate": 0.95,      # 95%
            "cache_hit_rate": 0.7,     # 70%
            "error_rate": 0.05         # 5%
        }
    
    async def check_and_alert(self, metrics: Dict[str, float]):
        """检查指标并发送告警"""
        for metric_name, value in metrics.items():
            threshold = self.alert_thresholds.get(metric_name)
            if threshold and self._should_alert(metric_name, value, threshold):
                await self._send_alert(metric_name, value, threshold)
```

## 总结

这些API整合优化建议将显著提升SynTour系统的可靠性、性能和可维护性。建议按照优先级逐步实施，并持续监控关键指标以确保优化效果。
