"""
AI Model Performance Monitoring and Optimization System
Tracks model performance, analyzes patterns, and triggers optimization actions
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import asyncio
import logging
import json
import time
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)

class PerformanceMetric(Enum):
    """Types of performance metrics to track"""
    RESPONSE_TIME = "response_time"
    TOKEN_USAGE = "token_usage"
    USER_SATISFACTION = "user_satisfaction"
    ERROR_RATE = "error_rate"
    CACHE_HIT_RATE = "cache_hit_rate"
    PROMPT_EFFECTIVENESS = "prompt_effectiveness"

@dataclass
class MetricEntry:
    """Individual metric entry with timestamp and metadata"""
    value: float
    timestamp: datetime
    metadata: Dict[str, Any] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "metadata": self.metadata or {}
        }

@dataclass
class PerformanceAlert:
    """Performance alert with severity and recommendations"""
    metric_type: PerformanceMetric
    severity: str  # "low", "medium", "high", "critical"
    current_value: float
    threshold: float
    message: str
    recommendations: List[str]
    timestamp: datetime

class ModelPerformanceMonitor:
    """
    Comprehensive model performance monitoring system
    Tracks various metrics and triggers optimization when needed
    """
    
    def __init__(self):
        self.metrics: Dict[PerformanceMetric, List[MetricEntry]] = {
            metric: [] for metric in PerformanceMetric
        }
        
        # Performance thresholds for alerts
        self.thresholds = {
            PerformanceMetric.RESPONSE_TIME: {
                "warning": 5.0,    # 5 seconds
                "critical": 10.0   # 10 seconds
            },
            PerformanceMetric.TOKEN_USAGE: {
                "warning": 3000,   # 3000 tokens
                "critical": 4000   # 4000 tokens
            },
            PerformanceMetric.USER_SATISFACTION: {
                "warning": 0.7,    # 70% satisfaction
                "critical": 0.5    # 50% satisfaction
            },
            PerformanceMetric.ERROR_RATE: {
                "warning": 0.05,   # 5% error rate
                "critical": 0.1    # 10% error rate
            },
            PerformanceMetric.CACHE_HIT_RATE: {
                "warning": 0.6,    # 60% cache hit rate
                "critical": 0.4    # 40% cache hit rate
            }
        }
        
        # Optimization strategies
        self.optimization_strategies = {
            PerformanceMetric.RESPONSE_TIME: self._optimize_response_time,
            PerformanceMetric.TOKEN_USAGE: self._optimize_token_usage,
            PerformanceMetric.USER_SATISFACTION: self._optimize_satisfaction,
            PerformanceMetric.ERROR_RATE: self._optimize_error_rate,
            PerformanceMetric.CACHE_HIT_RATE: self._optimize_cache_performance
        }
        
        # Recent alerts to avoid spam
        self.recent_alerts: List[PerformanceAlert] = []
        self.alert_cooldown = timedelta(minutes=30)
        
        logger.info("Model Performance Monitor initialized")
    
    async def track_model_performance(
        self,
        request_data: Dict[str, Any],
        response_data: Dict[str, Any],
        user_feedback: Optional[Dict[str, Any]] = None,
        processing_time: Optional[float] = None
    ):
        """
        Track comprehensive model performance metrics
        
        Args:
            request_data: Original request information
            response_data: Model response data
            user_feedback: Optional user feedback
            processing_time: Time taken to process request
        """
        try:
            current_time = datetime.now()
            
            # Track response time
            if processing_time:
                await self._record_metric(
                    PerformanceMetric.RESPONSE_TIME,
                    processing_time,
                    {"request_type": request_data.get("type", "unknown")}
                )
            
            # Track token usage
            token_count = response_data.get("token_count", 0)
            if token_count > 0:
                await self._record_metric(
                    PerformanceMetric.TOKEN_USAGE,
                    token_count,
                    {
                        "prompt_type": response_data.get("prompt_type", "unknown"),
                        "response_length": len(response_data.get("response", ""))
                    }
                )
            
            # Track user satisfaction
            if user_feedback:
                satisfaction = user_feedback.get("satisfaction_score", 0)
                if satisfaction > 0:
                    await self._record_metric(
                        PerformanceMetric.USER_SATISFACTION,
                        satisfaction,
                        {
                            "feedback_type": user_feedback.get("type", "general"),
                            "user_comment": bool(user_feedback.get("comment"))
                        }
                    )
            
            # Track errors
            if response_data.get("error") or not response_data.get("success", True):
                await self._record_metric(
                    PerformanceMetric.ERROR_RATE,
                    1.0,  # Error occurred
                    {
                        "error_type": response_data.get("error_code", "unknown"),
                        "error_message": response_data.get("error", "")
                    }
                )
            else:
                await self._record_metric(
                    PerformanceMetric.ERROR_RATE,
                    0.0,  # No error
                    {"success": True}
                )
            
            # Track cache performance
            cache_hit = response_data.get("cache_hit", False)
            await self._record_metric(
                PerformanceMetric.CACHE_HIT_RATE,
                1.0 if cache_hit else 0.0,
                {"cache_source": response_data.get("cache_source", "none")}
            )
            
            # Check for performance issues
            await self._check_performance_thresholds()
            
        except Exception as e:
            logger.error(f"Error tracking model performance: {e}")
    
    async def _record_metric(
        self,
        metric_type: PerformanceMetric,
        value: float,
        metadata: Dict[str, Any] = None
    ):
        """Record a performance metric with timestamp"""
        
        entry = MetricEntry(
            value=value,
            timestamp=datetime.now(),
            metadata=metadata
        )
        
        self.metrics[metric_type].append(entry)
        
        # Keep only recent metrics (last 24 hours)
        cutoff_time = datetime.now() - timedelta(hours=24)
        self.metrics[metric_type] = [
            entry for entry in self.metrics[metric_type]
            if entry.timestamp > cutoff_time
        ]
    
    async def _check_performance_thresholds(self):
        """Check if any metrics exceed performance thresholds"""
        
        for metric_type in PerformanceMetric:
            try:
                recent_metrics = self._get_recent_metrics(metric_type, hours=1)
                if not recent_metrics:
                    continue
                
                # Calculate current performance
                current_value = await self._calculate_current_performance(metric_type, recent_metrics)
                
                # Check thresholds
                thresholds = self.thresholds.get(metric_type, {})
                
                if current_value <= thresholds.get("critical", float('inf')):
                    await self._trigger_alert(metric_type, "critical", current_value, thresholds["critical"])
                elif current_value <= thresholds.get("warning", float('inf')):
                    await self._trigger_alert(metric_type, "warning", current_value, thresholds["warning"])
                
            except Exception as e:
                logger.error(f"Error checking thresholds for {metric_type}: {e}")
    
    def _get_recent_metrics(self, metric_type: PerformanceMetric, hours: int = 1) -> List[MetricEntry]:
        """Get metrics from the last N hours"""
        
        cutoff_time = datetime.now() - timedelta(hours=hours)
        return [
            entry for entry in self.metrics[metric_type]
            if entry.timestamp > cutoff_time
        ]
    
    async def _calculate_current_performance(
        self,
        metric_type: PerformanceMetric,
        recent_metrics: List[MetricEntry]
    ) -> float:
        """Calculate current performance value for a metric"""
        
        if not recent_metrics:
            return 0.0
        
        values = [entry.value for entry in recent_metrics]
        
        if metric_type in [PerformanceMetric.RESPONSE_TIME, PerformanceMetric.TOKEN_USAGE]:
            # For these metrics, we want the average
            return sum(values) / len(values)
        
        elif metric_type == PerformanceMetric.USER_SATISFACTION:
            # Average satisfaction score
            return sum(values) / len(values)
        
        elif metric_type == PerformanceMetric.ERROR_RATE:
            # Percentage of errors
            return sum(values) / len(values)
        
        elif metric_type == PerformanceMetric.CACHE_HIT_RATE:
            # Percentage of cache hits
            return sum(values) / len(values)
        
        else:
            return sum(values) / len(values)
    
    async def _trigger_alert(
        self,
        metric_type: PerformanceMetric,
        severity: str,
        current_value: float,
        threshold: float
    ):
        """Trigger a performance alert and optimization"""
        
        # Check if we've already alerted for this metric recently
        recent_alerts = [
            alert for alert in self.recent_alerts
            if (alert.metric_type == metric_type and
                datetime.now() - alert.timestamp < self.alert_cooldown)
        ]
        
        if recent_alerts:
            return  # Skip duplicate alerts
        
        # Generate alert
        alert = PerformanceAlert(
            metric_type=metric_type,
            severity=severity,
            current_value=current_value,
            threshold=threshold,
            message=self._generate_alert_message(metric_type, severity, current_value, threshold),
            recommendations=self._generate_recommendations(metric_type, severity),
            timestamp=datetime.now()
        )
        
        self.recent_alerts.append(alert)
        
        # Log the alert
        logger.warning(f"Performance Alert: {alert.message}")
        
        # Trigger optimization if critical
        if severity == "critical":
            await self._trigger_optimization(metric_type, current_value)
    
    def _generate_alert_message(
        self,
        metric_type: PerformanceMetric,
        severity: str,
        current_value: float,
        threshold: float
    ) -> str:
        """Generate human-readable alert message"""
        
        metric_name = metric_type.value.replace("_", " ").title()
        
        if metric_type == PerformanceMetric.RESPONSE_TIME:
            return f"{severity.upper()}: {metric_name} is {current_value:.2f}s (threshold: {threshold}s)"
        elif metric_type == PerformanceMetric.TOKEN_USAGE:
            return f"{severity.upper()}: {metric_name} is {current_value:.0f} tokens (threshold: {threshold})"
        elif metric_type in [PerformanceMetric.USER_SATISFACTION, PerformanceMetric.CACHE_HIT_RATE]:
            return f"{severity.upper()}: {metric_name} is {current_value:.1%} (threshold: {threshold:.1%})"
        elif metric_type == PerformanceMetric.ERROR_RATE:
            return f"{severity.upper()}: {metric_name} is {current_value:.1%} (threshold: {threshold:.1%})"
        else:
            return f"{severity.upper()}: {metric_name} is {current_value} (threshold: {threshold})"
    
    def _generate_recommendations(self, metric_type: PerformanceMetric, severity: str) -> List[str]:
        """Generate optimization recommendations"""
        
        recommendations = {
            PerformanceMetric.RESPONSE_TIME: [
                "Optimize prompt length and complexity",
                "Implement response caching",
                "Consider model parameter tuning",
                "Review API timeout settings"
            ],
            PerformanceMetric.TOKEN_USAGE: [
                "Implement intelligent prompt truncation",
                "Use more concise prompt templates",
                "Optimize response length controls",
                "Consider prompt compression techniques"
            ],
            PerformanceMetric.USER_SATISFACTION: [
                "Analyze user feedback patterns",
                "Improve prompt engineering",
                "Enhance response relevance",
                "Consider model fine-tuning"
            ],
            PerformanceMetric.ERROR_RATE: [
                "Review error handling mechanisms",
                "Improve input validation",
                "Enhance fallback strategies",
                "Monitor API dependencies"
            ],
            PerformanceMetric.CACHE_HIT_RATE: [
                "Optimize cache key generation",
                "Adjust cache TTL settings",
                "Improve cache warming strategies",
                "Review cache invalidation logic"
            ]
        }
        
        return recommendations.get(metric_type, ["Review system performance"])
    
    async def _trigger_optimization(self, metric_type: PerformanceMetric, current_value: float):
        """Trigger automatic optimization for critical performance issues"""
        
        try:
            optimizer = self.optimization_strategies.get(metric_type)
            if optimizer:
                logger.info(f"Triggering optimization for {metric_type.value}")
                await optimizer(current_value)
            else:
                logger.warning(f"No optimization strategy available for {metric_type.value}")
                
        except Exception as e:
            logger.error(f"Optimization failed for {metric_type.value}: {e}")
    
    async def _optimize_response_time(self, current_value: float):
        """Optimize response time performance"""
        
        logger.info(f"Optimizing response time (current: {current_value:.2f}s)")
        
        # Implementation would include:
        # - Adjusting model parameters for faster inference
        # - Implementing response streaming
        # - Optimizing prompt templates
        # - Enabling aggressive caching
        
        # Placeholder implementation
        optimization_actions = [
            "Reduced model temperature for faster generation",
            "Enabled response streaming for long outputs",
            "Optimized prompt templates for efficiency",
            "Increased cache TTL for frequent queries"
        ]
        
        logger.info(f"Response time optimization actions: {optimization_actions}")
    
    async def _optimize_token_usage(self, current_value: float):
        """Optimize token usage efficiency"""
        
        logger.info(f"Optimizing token usage (current: {current_value:.0f} tokens)")
        
        # Implementation would include:
        # - Implementing dynamic prompt truncation
        # - Using more efficient prompt templates
        # - Adjusting max_output_tokens based on request type
        
        optimization_actions = [
            "Implemented dynamic prompt truncation",
            "Switched to more efficient prompt templates",
            "Adjusted max_output_tokens based on request complexity"
        ]
        
        logger.info(f"Token usage optimization actions: {optimization_actions}")
    
    async def _optimize_satisfaction(self, current_value: float):
        """Optimize user satisfaction"""
        
        logger.info(f"Optimizing user satisfaction (current: {current_value:.1%})")
        
        # Implementation would include:
        # - Analyzing feedback patterns
        # - A/B testing different prompt strategies
        # - Improving response personalization
        
        optimization_actions = [
            "Analyzed recent user feedback patterns",
            "Implemented improved prompt personalization",
            "Enhanced response relevance scoring"
        ]
        
        logger.info(f"Satisfaction optimization actions: {optimization_actions}")
    
    async def _optimize_error_rate(self, current_value: float):
        """Optimize error rate"""
        
        logger.info(f"Optimizing error rate (current: {current_value:.1%})")
        
        # Implementation would include:
        # - Improving input validation
        # - Enhancing error handling
        # - Implementing better fallback mechanisms
        
        optimization_actions = [
            "Enhanced input validation rules",
            "Improved error handling and recovery",
            "Implemented better fallback response mechanisms"
        ]
        
        logger.info(f"Error rate optimization actions: {optimization_actions}")
    
    async def _optimize_cache_performance(self, current_value: float):
        """Optimize cache performance"""
        
        logger.info(f"Optimizing cache performance (current: {current_value:.1%})")
        
        # Implementation would include:
        # - Adjusting cache key strategies
        # - Optimizing TTL settings
        # - Implementing cache warming
        
        optimization_actions = [
            "Optimized cache key generation strategy",
            "Adjusted TTL settings based on content type",
            "Implemented proactive cache warming"
        ]
        
        logger.info(f"Cache optimization actions: {optimization_actions}")
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        
        summary = {
            "period": f"Last {hours} hours",
            "timestamp": datetime.now().isoformat(),
            "metrics": {},
            "alerts": [],
            "overall_health": "good"
        }
        
        health_scores = []
        
        for metric_type in PerformanceMetric:
            recent_metrics = self._get_recent_metrics(metric_type, hours)
            
            if recent_metrics:
                current_value = sum(entry.value for entry in recent_metrics) / len(recent_metrics)
                
                # Determine health status
                thresholds = self.thresholds.get(metric_type, {})
                if current_value <= thresholds.get("critical", float('inf')):
                    health = "critical"
                    health_scores.append(0.2)
                elif current_value <= thresholds.get("warning", float('inf')):
                    health = "warning"
                    health_scores.append(0.6)
                else:
                    health = "good"
                    health_scores.append(1.0)
                
                summary["metrics"][metric_type.value] = {
                    "current_value": current_value,
                    "sample_count": len(recent_metrics),
                    "health": health
                }
            else:
                summary["metrics"][metric_type.value] = {
                    "current_value": None,
                    "sample_count": 0,
                    "health": "no_data"
                }
        
        # Recent alerts
        recent_alerts = [
            alert for alert in self.recent_alerts
            if datetime.now() - alert.timestamp < timedelta(hours=hours)
        ]
        
        summary["alerts"] = [
            {
                "metric": alert.metric_type.value,
                "severity": alert.severity,
                "message": alert.message,
                "timestamp": alert.timestamp.isoformat()
            }
            for alert in recent_alerts
        ]
        
        # Overall health
        if health_scores:
            avg_health = sum(health_scores) / len(health_scores)
            if avg_health >= 0.8:
                summary["overall_health"] = "good"
            elif avg_health >= 0.5:
                summary["overall_health"] = "warning"
            else:
                summary["overall_health"] = "critical"
        
        return summary
    
    async def cleanup_old_data(self, days: int = 7):
        """Clean up old performance data"""
        
        cutoff_time = datetime.now() - timedelta(days=days)
        
        for metric_type in PerformanceMetric:
            original_count = len(self.metrics[metric_type])
            self.metrics[metric_type] = [
                entry for entry in self.metrics[metric_type]
                if entry.timestamp > cutoff_time
            ]
            cleaned_count = original_count - len(self.metrics[metric_type])
            
            if cleaned_count > 0:
                logger.info(f"Cleaned {cleaned_count} old {metric_type.value} entries")
        
        # Clean old alerts
        self.recent_alerts = [
            alert for alert in self.recent_alerts
            if datetime.now() - alert.timestamp < timedelta(days=1)
        ]
