# app/core/test_coverage.py
import asyncio
import time
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

@dataclass
class TestResult:
    """Test result data structure"""
    test_name: str
    success: bool
    duration: float
    error_message: Optional[str] = None
    response_data: Optional[Any] = None
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()

@dataclass
class APITestSuite:
    """API test suite results"""
    api_name: str
    total_tests: int
    passed_tests: int
    failed_tests: int
    success_rate: float
    total_duration: float
    test_results: List[TestResult]
    timestamp: str = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.utcnow().isoformat()

class TestCoverageManager:
    """Manages test coverage and execution for API services"""
    
    def __init__(self):
        self.test_results: Dict[str, APITestSuite] = {}
        self.test_registry: Dict[str, List[Callable]] = {}
    
    def register_test(self, api_name: str, test_function: Callable, test_name: Optional[str] = None):
        """Register a test function for an API"""
        if api_name not in self.test_registry:
            self.test_registry[api_name] = []
        
        # Store test function with metadata
        test_info = {
            'function': test_function,
            'name': test_name or test_function.__name__,
            'doc': test_function.__doc__ or 'No description'
        }
        
        self.test_registry[api_name].append(test_info)
    
    async def run_single_test(self, test_function: Callable, test_name: str) -> TestResult:
        """Run a single test function"""
        start_time = time.time()
        
        try:
            if asyncio.iscoroutinefunction(test_function):
                result = await test_function()
            else:
                result = test_function()
            
            duration = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                success=True,
                duration=duration,
                response_data=result
            )
            
        except Exception as e:
            duration = time.time() - start_time
            
            return TestResult(
                test_name=test_name,
                success=False,
                duration=duration,
                error_message=str(e)
            )
    
    async def run_api_tests(self, api_name: str) -> APITestSuite:
        """Run all tests for a specific API"""
        if api_name not in self.test_registry:
            logger.warning(f"No tests registered for API: {api_name}")
            return APITestSuite(
                api_name=api_name,
                total_tests=0,
                passed_tests=0,
                failed_tests=0,
                success_rate=0.0,
                total_duration=0.0,
                test_results=[]
            )
        
        test_results = []
        start_time = time.time()
        
        for test_info in self.test_registry[api_name]:
            result = await self.run_single_test(
                test_info['function'], 
                test_info['name']
            )
            test_results.append(result)
        
        total_duration = time.time() - start_time
        passed_tests = sum(1 for r in test_results if r.success)
        failed_tests = len(test_results) - passed_tests
        success_rate = (passed_tests / len(test_results)) * 100 if test_results else 0
        
        suite_result = APITestSuite(
            api_name=api_name,
            total_tests=len(test_results),
            passed_tests=passed_tests,
            failed_tests=failed_tests,
            success_rate=success_rate,
            total_duration=total_duration,
            test_results=test_results
        )
        
        self.test_results[api_name] = suite_result
        return suite_result
    
    async def run_all_tests(self) -> Dict[str, APITestSuite]:
        """Run all registered tests"""
        results = {}
        
        for api_name in self.test_registry.keys():
            results[api_name] = await self.run_api_tests(api_name)
        
        return results
    
    def get_coverage_report(self) -> Dict[str, Any]:
        """Generate comprehensive coverage report"""
        total_tests = sum(suite.total_tests for suite in self.test_results.values())
        total_passed = sum(suite.passed_tests for suite in self.test_results.values())
        total_failed = sum(suite.failed_tests for suite in self.test_results.values())
        
        overall_success_rate = (total_passed / total_tests) * 100 if total_tests > 0 else 0
        
        api_coverage = {}
        for api_name, suite in self.test_results.items():
            api_coverage[api_name] = {
                'success_rate': suite.success_rate,
                'total_tests': suite.total_tests,
                'passed_tests': suite.passed_tests,
                'failed_tests': suite.failed_tests,
                'duration': suite.total_duration,
                'status': 'GOOD' if suite.success_rate >= 80 else 'POOR' if suite.success_rate >= 50 else 'CRITICAL'
            }
        
        return {
            'overall_statistics': {
                'total_tests': total_tests,
                'passed_tests': total_passed,
                'failed_tests': total_failed,
                'success_rate': overall_success_rate,
                'apis_tested': len(self.test_results),
                'timestamp': datetime.utcnow().isoformat()
            },
            'api_coverage': api_coverage,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """Generate recommendations based on test results"""
        recommendations = []
        
        for api_name, suite in self.test_results.items():
            if suite.success_rate < 50:
                recommendations.append(f"CRITICAL: {api_name} has {suite.success_rate:.1f}% success rate - immediate attention required")
            elif suite.success_rate < 80:
                recommendations.append(f"WARNING: {api_name} has {suite.success_rate:.1f}% success rate - needs improvement")
            elif suite.total_tests < 3:
                recommendations.append(f"INFO: {api_name} has only {suite.total_tests} tests - consider adding more test cases")
        
        if not recommendations:
            recommendations.append("All APIs have good test coverage and success rates")
        
        return recommendations
    
    def export_results(self, format: str = 'json') -> str:
        """Export test results in specified format"""
        if format.lower() == 'json':
            return json.dumps({
                'test_results': {api: asdict(suite) for api, suite in self.test_results.items()},
                'coverage_report': self.get_coverage_report()
            }, indent=2, default=str)
        else:
            raise ValueError(f"Unsupported export format: {format}")

# Global test coverage manager instance
test_coverage_manager = TestCoverageManager()

# Decorator for registering tests
def api_test(api_name: str, test_name: Optional[str] = None):
    """Decorator to register API tests"""
    def decorator(func):
        test_coverage_manager.register_test(api_name, func, test_name)
        return func
    return decorator

# Test helper functions
async def test_api_connectivity(api_function: Callable, *args, **kwargs) -> bool:
    """Test basic API connectivity"""
    try:
        result = await api_function(*args, **kwargs) if asyncio.iscoroutinefunction(api_function) else api_function(*args, **kwargs)
        return result is not None
    except Exception:
        return False

async def test_api_response_time(api_function: Callable, max_time: float = 5.0, *args, **kwargs) -> bool:
    """Test API response time"""
    start_time = time.time()
    try:
        if asyncio.iscoroutinefunction(api_function):
            await api_function(*args, **kwargs)
        else:
            api_function(*args, **kwargs)
        
        duration = time.time() - start_time
        return duration <= max_time
    except Exception:
        return False

async def test_api_error_handling(api_function: Callable, invalid_params: Dict[str, Any]) -> bool:
    """Test API error handling with invalid parameters"""
    try:
        if asyncio.iscoroutinefunction(api_function):
            await api_function(**invalid_params)
        else:
            api_function(**invalid_params)
        
        # If no exception was raised, error handling might be insufficient
        return False
    except Exception:
        # Exception was properly raised for invalid input
        return True

def create_test_data_generator(api_name: str) -> Dict[str, Any]:
    """Generate test data for specific APIs"""
    test_data = {
        'google_places': {
            'valid_place_id': 'ChIJN1t_tDeuEmsRUsoyG83frY4',  # Google Sydney
            'invalid_place_id': 'invalid_place_id_123',
            'valid_fields': ['place_id', 'name', 'rating'],
            'invalid_fields': ['invalid_field']
        },
        'flightapi': {
            'valid_origin': 'JFK',
            'valid_destination': 'LAX',
            'valid_date': '2024-12-25',
            'invalid_origin': 'INVALID',
            'invalid_date': '2023-01-01'  # Past date
        },
        'openweather': {
            'valid_city': 'London',
            'valid_coordinates': {'lat': 51.5074, 'lon': -0.1278},
            'invalid_city': 'NonExistentCity123',
            'invalid_coordinates': {'lat': 999, 'lon': 999}
        }
    }
    
    return test_data.get(api_name.lower(), {})

# Edge case test generators
def generate_edge_case_tests(api_name: str) -> List[Dict[str, Any]]:
    """Generate edge case test scenarios"""
    edge_cases = {
        'google_places': [
            {'test': 'empty_place_id', 'params': {'place_id': ''}},
            {'test': 'very_long_place_id', 'params': {'place_id': 'x' * 1000}},
            {'test': 'special_characters', 'params': {'place_id': '!@#$%^&*()'}},
        ],
        'flightapi': [
            {'test': 'same_origin_destination', 'params': {'origin': 'JFK', 'destination': 'JFK'}},
            {'test': 'future_date_far', 'params': {'departure_date': '2030-12-31'}},
            {'test': 'negative_passengers', 'params': {'adults': -1}},
        ],
        'openweather': [
            {'test': 'extreme_coordinates', 'params': {'lat': 90, 'lon': 180}},
            {'test': 'zero_coordinates', 'params': {'lat': 0, 'lon': 0}},
            {'test': 'empty_city_name', 'params': {'city': ''}},
        ]
    }
    
    return edge_cases.get(api_name.lower(), [])