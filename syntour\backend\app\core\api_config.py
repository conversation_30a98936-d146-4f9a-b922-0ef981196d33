# app/core/api_config.py
from typing import Dict, Any, Optional
from dataclasses import dataclass
import os
from enum import Enum

class Environment(str, Enum):
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"

@dataclass
class APIConfig:
    """Unified API configuration"""
    
    # Timeout settings (seconds)
    DEFAULT_TIMEOUT: int = 30
    QUICK_TIMEOUT: int = 10
    LONG_TIMEOUT: int = 60
    
    # Retry settings
    MAX_RETRIES: int = 3
    RETRY_BACKOFF_FACTOR: float = 1.5
    RETRY_STATUSES: tuple = (429, 500, 502, 503, 504)
    
    # Rate limiting
    DEFAULT_RATE_LIMIT: int = 100  # requests per minute
    BURST_RATE_LIMIT: int = 10     # requests per second
    
    # Cache settings
    DEFAULT_CACHE_TTL: int = 300   # 5 minutes
    LONG_CACHE_TTL: int = 3600     # 1 hour
    SHORT_CACHE_TTL: int = 60      # 1 minute
    
    # File upload settings
    MAX_FILE_SIZE_MB: int = 10
    GCS_UPLOAD_THRESHOLD_MB: int = 20  # Files stored in GCS if total size + user text > 20MB
    ALLOWED_FILE_TYPES: tuple = ('.jpg', '.jpeg', '.png', '.pdf', '.txt', '.docx')
    
    # Security settings
    LOG_SENSITIVE_DATA: bool = False
    MASK_API_KEYS: bool = True
    
    @classmethod
    def get_environment(cls) -> Environment:
        """Get current environment"""
        env = os.getenv('ENVIRONMENT', 'development').lower()
        try:
            return Environment(env)
        except ValueError:
            return Environment.DEVELOPMENT
    
    @classmethod
    def get_cors_origins(cls) -> list:
        """Get CORS origins based on environment"""
        env = cls.get_environment()
        
        if env == Environment.PRODUCTION:
            return [
                "https://syntour.com",
                "https://www.syntour.com",
                "https://api.syntour.com"
            ]
        elif env == Environment.STAGING:
            return [
                "https://staging.syntour.com",
                "http://localhost:3000",
                "http://localhost:8000"
            ]
        else:  # Development
            return [
                "http://localhost:3000",
                "http://localhost:8080",
                "http://localhost:5173",
                "http://127.0.0.1:3000",
                "http://127.0.0.1:8000",
                "http://127.0.0.1:5173"
            ]
    
    @classmethod
    def get_log_level(cls) -> str:
        """Get log level based on environment"""
        env = cls.get_environment()
        
        if env == Environment.PRODUCTION:
            return "WARNING"
        elif env == Environment.STAGING:
            return "INFO"
        else:
            return "DEBUG"

# API-specific configurations
API_CONFIGS = {
    "amadeus": {
        "base_url": "https://test.api.amadeus.com",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 50,  # requests per minute
        "cache_ttl": APIConfig.LONG_CACHE_TTL
    },
    "hotelbeds": {
        "base_url": "https://api.test.hotelbeds.com",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 100,
        "cache_ttl": APIConfig.DEFAULT_CACHE_TTL
    },
    "geoapify": {
        "base_url": "https://api.geoapify.com/v2",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 1000,
        "cache_ttl": APIConfig.LONG_CACHE_TTL
    },
    "google_places": {
        "base_url": "https://maps.googleapis.com/maps/api/place",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 100,
        "cache_ttl": APIConfig.LONG_CACHE_TTL
    },
    "openweather": {
        "base_url": "https://api.openweathermap.org/data/2.5",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 60,
        "cache_ttl": APIConfig.DEFAULT_CACHE_TTL
    },
    "tomorrow_io": {
        "base_url": "https://api.tomorrow.io/v4",
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": 100,
        "cache_ttl": APIConfig.DEFAULT_CACHE_TTL
    },
    "flight_api": {
        "base_url": "https://api.flightapi.io",
        "timeout": APIConfig.LONG_TIMEOUT,  # Flight searches can be slow
        "rate_limit": 50,
        "cache_ttl": APIConfig.SHORT_CACHE_TTL
    }
}

def get_api_config(api_name: str) -> Dict[str, Any]:
    """Get configuration for specific API"""
    return API_CONFIGS.get(api_name, {
        "timeout": APIConfig.DEFAULT_TIMEOUT,
        "rate_limit": APIConfig.DEFAULT_RATE_LIMIT,
        "cache_ttl": APIConfig.DEFAULT_CACHE_TTL
    })