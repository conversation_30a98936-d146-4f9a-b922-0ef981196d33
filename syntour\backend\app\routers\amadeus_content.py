# app/routers/amadeus_content.py
from fastapi import APIRouter, Query, HTTPException, Body
from typing import Optional
from datetime import datetime, date

from app.services.amadeus_service import (
    search_flight_offers,
    search_flight_offers_post,
    get_flight_status,
    search_cheapest_flight_dates,
    search_airports,
    get_airline_routes,
    validate_date_format,
    validate_iata_code
)
from app.models.amadeus_model import (
    FlightOffersResponse,
    FlightStatusResponse,
    FlightDatesResponse,
    FlightSearchParams,
    FlightStatusParams,
    FlightDatesParams,
    FlightOffersSearchRequest,
    TravelClass,
    AmadeusErrorResponse
)

router = APIRouter(prefix="/amadeus", tags=["Amadeus Flight API"])

@router.get("/flight-offers", response_model=FlightOffersResponse)
async def search_flights(
    originLocationCode: str = Query(..., description="IATA code for origin airport (e.g., BOS)"),
    destinationLocationCode: str = Query(..., description="IATA code for destination airport (e.g., CHI)"),
    departureDate: str = Query(..., description="Departure date in YYYY-MM-DD format"),
    returnDate: Optional[str] = Query(None, description="Return date in YYYY-MM-DD format (for round-trip)"),
    adults: int = Query(1, ge=1, le=9, description="Number of adult passengers (1-9)"),
    children: Optional[int] = Query(None, ge=0, le=9, description="Number of child passengers (0-9)"),
    infants: Optional[int] = Query(None, ge=0, le=9, description="Number of infant passengers (0-9)"),
    travelClass: Optional[TravelClass] = Query(None, description="Travel class preference"),
    includedAirlineCodes: Optional[str] = Query(None, description="Comma-separated airline codes to include (e.g., BA,LH)"),
    excludedAirlineCodes: Optional[str] = Query(None, description="Comma-separated airline codes to exclude (e.g., AA,DL)"),
    nonStop: Optional[bool] = Query(None, description="Search for non-stop flights only"),
    currencyCode: str = Query("USD", description="Currency code for prices (e.g., USD, EUR)"),
    maxPrice: Optional[int] = Query(None, ge=1, description="Maximum price filter"),
    max: Optional[int] = Query(None, ge=1, le=250, description="Maximum number of results (1-250)")
):
    """
    Search for flight offers between two cities.
    
    This endpoint searches over 500 airlines to find the best flight offers
    for a given itinerary. Returns flight options with prices, schedules,
    and airline information.
    """
    # Validate input parameters
    if not validate_iata_code(originLocationCode):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if not validate_iata_code(destinationLocationCode):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    if not validate_date_format(departureDate):
        raise HTTPException(status_code=400, detail="Invalid departure date format. Use YYYY-MM-DD")
    
    if returnDate and not validate_date_format(returnDate):
        raise HTTPException(status_code=400, detail="Invalid return date format. Use YYYY-MM-DD")
    
    # Check if departure date is not in the past
    departure_dt = datetime.strptime(departureDate, "%Y-%m-%d").date()
    if departure_dt < date.today():
        raise HTTPException(status_code=400, detail="Departure date cannot be in the past")
    
    # Check if return date is after departure date
    if returnDate:
        return_dt = datetime.strptime(returnDate, "%Y-%m-%d").date()
        if return_dt <= departure_dt:
            raise HTTPException(status_code=400, detail="Return date must be after departure date")
    
    return await search_flight_offers(
        origin_location_code=originLocationCode,
        destination_location_code=destinationLocationCode,
        departure_date=departureDate,
        return_date=returnDate,
        adults=adults,
        children=children,
        infants=infants,
        travel_class=travelClass.value if travelClass else None,
        included_airline_codes=includedAirlineCodes,
        excluded_airline_codes=excludedAirlineCodes,
        non_stop=nonStop,
        currency_code=currencyCode,
        max_price=maxPrice,
        max_results=max
    )

@router.post("/flight-offers", response_model=FlightOffersResponse)
async def search_flights_advanced(
    request: FlightOffersSearchRequest = Body(..., description="Advanced flight search request")
):
    """
    Advanced flight search with full functionality using POST method.
    
    This endpoint provides access to all advanced search features including:
    - Multi-city searches
    - Complex traveler configurations
    - Detailed flight filters
    - Cabin restrictions
    - Carrier restrictions
    """
    # Convert Pydantic model to dict for the service
    request_data = request.dict(exclude_none=True)
    
    # Validate origin destinations
    for od in request.originDestinations:
        if not validate_iata_code(od.originLocationCode):
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid origin IATA code: {od.originLocationCode}"
            )
        if not validate_iata_code(od.destinationLocationCode):
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid destination IATA code: {od.destinationLocationCode}"
            )
        if not validate_date_format(od.departureDateTimeRange.date):
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid date format in origin destination {od.id}"
            )
    
    return await search_flight_offers_post(request_data)

@router.get("/flight-status", response_model=FlightStatusResponse)
async def check_flight_status(
    carrierCode: str = Query(..., description="IATA airline code (e.g., IB for Iberia)"),
    flightNumber: str = Query(..., description="Flight number (e.g., 532)"),
    scheduledDepartureDate: str = Query(..., description="Scheduled departure date in YYYY-MM-DD format"),
    operationalSuffix: Optional[str] = Query(None, description="Operational suffix (single letter, e.g., A)")
):
    """
    Get real-time flight status information.
    
    Provides up-to-date departure and arrival times, terminal and gate information,
    flight duration, and real-time delay status for a specific flight.
    """
    # Validate input parameters
    if len(carrierCode) != 2 or not carrierCode.isalpha() or not carrierCode.isupper():
        raise HTTPException(status_code=400, detail="Invalid carrier code format. Use 2-letter IATA code (e.g., IB)")
    
    if not flightNumber.isdigit():
        raise HTTPException(status_code=400, detail="Flight number must be numeric")
    
    if not validate_date_format(scheduledDepartureDate):
        raise HTTPException(status_code=400, detail="Invalid date format. Use YYYY-MM-DD")
    
    if operationalSuffix and (len(operationalSuffix) != 1 or not operationalSuffix.isalpha()):
        raise HTTPException(status_code=400, detail="Operational suffix must be a single letter")
    
    return await get_flight_status(
        carrier_code=carrierCode,
        flight_number=flightNumber,
        scheduled_departure_date=scheduledDepartureDate,
        operational_suffix=operationalSuffix
    )

@router.get("/cheapest-dates", response_model=FlightDatesResponse)
async def search_cheapest_dates(
    origin: str = Query(..., description="IATA code for origin airport"),
    destination: Optional[str] = Query(None, description="IATA code for destination airport"),
    departureDate: Optional[str] = Query(None, description="Departure date(s) in YYYY-MM-DD format (comma-separated for multiple)"),
    oneWay: Optional[bool] = Query(None, description="Search for one-way flights only"),
    duration: Optional[int] = Query(None, ge=1, le=365, description="Trip duration in days (for one-way searches)"),
    nonStop: Optional[bool] = Query(None, description="Search for non-stop flights only"),
    maxPrice: Optional[float] = Query(None, ge=0, description="Maximum price filter"),
    viewBy: Optional[str] = Query(None, description="View results by DATE, DURATION, or WEEK")
):
    """
    Find the cheapest dates to travel between cities.
    
    This endpoint helps find the most affordable travel dates by providing
    a list of flight options with dates and prices. Results can be ordered
    by price, departure date, or duration.
    
    Note: This API returns cached prices. Use the flight-offers endpoint
    for real-time pricing once dates are chosen.
    """
    # Validate input parameters
    if not validate_iata_code(origin):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if destination and not validate_iata_code(destination):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    # Validate departure dates if provided
    if departureDate:
        dates = departureDate.split(",")
        for date_str in dates:
            date_str = date_str.strip()
            if not validate_date_format(date_str):
                raise HTTPException(status_code=400, detail=f"Invalid date format: {date_str}. Use YYYY-MM-DD")
    
    if viewBy and viewBy not in ["DATE", "DURATION", "WEEK"]:
        raise HTTPException(status_code=400, detail="viewBy must be one of: DATE, DURATION, WEEK")
    
    return await search_cheapest_flight_dates(
        origin=origin,
        destination=destination,
        departure_date=departureDate,
        one_way=oneWay,
        duration=duration,
        non_stop=nonStop,
        max_price=maxPrice,
        view_by=viewBy
    )

@router.get("/airports")
async def search_airports_by_keyword(
    keyword: str = Query(..., min_length=1, description="Search keyword (city name, airport name, or IATA code)"),
    subType: str = Query("AIRPORT", description="Location subtype (AIRPORT, CITY)")
):
    """
    Search for airports by keyword.
    
    Find airports by searching with city names, airport names, or IATA codes.
    Useful for implementing airport search functionality in booking forms.
    """
    if len(keyword.strip()) < 1:
        raise HTTPException(status_code=400, detail="Keyword must not be empty")
    
    if subType not in ["AIRPORT", "CITY"]:
        raise HTTPException(status_code=400, detail="subType must be either AIRPORT or CITY")
    
    return await search_airports(keyword=keyword.strip(), subtype=subType)

@router.get("/airline-routes")
async def get_airline_destinations(
    originIataCode: str = Query(..., description="IATA code for origin airport"),
    destinationIataCode: Optional[str] = Query(None, description="IATA code for destination airport (optional)")
):
    """
    Get airline route information.
    
    Retrieve information about airline routes from a specific origin,
    optionally filtered by destination.
    """
    if not validate_iata_code(originIataCode):
        raise HTTPException(status_code=400, detail="Invalid origin IATA code format")
    
    if destinationIataCode and not validate_iata_code(destinationIataCode):
        raise HTTPException(status_code=400, detail="Invalid destination IATA code format")
    
    return await get_airline_routes(
        origin_iata_code=originIataCode,
        destination_iata_code=destinationIataCode
    )

@router.get("/health")
async def health_check():
    """Health check endpoint for Amadeus API integration"""
    return {
        "status": "healthy",
        "service": "amadeus-api",
        "endpoints": [
            "flight-offers",
            "flight-status", 
            "cheapest-dates",
            "airports",
            "airline-routes"
        ]
    }