from fastapi import FastAP<PERSON>, Request, HTTPException, UploadFile, File, Form, BackgroundTasks, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse, JSONResponse
from app.routers.hotelbeds_content import router as hotelbeds_router
from pydantic import BaseModel, Field
import uvicorn

from typing import Optional, List, Dict, Any, Union
import os
from dotenv import load_dotenv
from datetime import datetime
import json
import logging
import asyncio
import time
import uuid
import tempfile
import mimetypes
import base64
from enum import Enum
import aiohttp

from app.core.api_validator import api_validator
from app.core.api_config import APIConfig
from app.core.error_handler import global_exception_handler
from app.core.logging_config import setup_logging
from app.middleware.rate_limiter import RateLimitMiddleware

from google.cloud import storage, speech
from google.cloud.speech_v1 import SpeechClient
from google.cloud.speech_v1.types import (
    RecognitionConfig,
    StreamingRecognitionConfig,
    StreamingRecognizeRequest
)
from google.oauth2 import service_account
from google import genai

from app.services.prompt_templates import PromptTemplates
from app.services.file_processor import FileProcessor

# Import new AI optimization modules
from app.core.prompt_engineering import AdvancedPromptEngine, PromptType
from app.core.multimodal_processor import MultimodalProcessor
from app.core.model_performance import ModelPerformanceMonitor

# Load environment variables
load_dotenv()

setup_logging()
logger = logging.getLogger(__name__)

MAX_FILE_SIZE_MB = APIConfig.MAX_FILE_SIZE_MB
MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024
MAX_AUDIO_BUFFER_SIZE = 1024 * 1024  # 1MB limit
GCS_UPLOAD_THRESHOLD_MB = APIConfig.GCS_UPLOAD_THRESHOLD_MB

# Error handling classes
class ErrorCode(str, Enum):
    VALIDATION_ERROR = "VALIDATION_ERROR"
    PROCESSING_ERROR = "PROCESSING_ERROR"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAILABLE"
    WEBSOCKET_ERROR = "WEBSOCKET_ERROR"
    FILE_ERROR = "FILE_ERROR"

class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    error_code: Optional[ErrorCode] = None
    timestamp: str = Field(default_factory=lambda: datetime.now().isoformat())
    details: Optional[Dict[str, Any]] = None

# Tourism chatbot prompt
TOURISM_PROMPT = """You are SynTour, an advanced AI tourism assistant designed to provide comprehensive, personalized travel guidance. Your expertise spans global destinations, cultural insights, practical travel advice, and real-time assistance.

Core Capabilities:
- Destination recommendations based on preferences, budget, and travel style
- Cultural insights and local customs guidance
- Practical travel planning (visas, weather, transportation, accommodation)
- Real-time assistance during travel
- Multimodal interaction (text, voice, images)
- Multilingual support with cultural sensitivity

Personality:
- Enthusiastic and knowledgeable about travel
- Culturally sensitive and respectful
- Practical and helpful
- Friendly and approachable
- Adaptable to different communication styles

Response Guidelines:
- Provide specific, actionable advice
- Include relevant details (costs, timing, locations)
- Suggest alternatives when appropriate
- Be culturally sensitive and respectful
- Ask clarifying questions when needed
- Offer both popular and off-the-beaten-path options

Always aim to inspire and facilitate amazing travel experiences while ensuring safety and cultural respect."""

app = FastAPI(
    title="SynTour AI API - Enhanced",
    description="Advanced FastAPI backend for SynTour travel planning with multimodal AI support",
    version="2.0.0",
)

app.add_middleware(
    CORSMiddleware,
    allow_origins=APIConfig.get_cors_origins(),
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.add_middleware(RateLimitMiddleware, default_rate_limit=100)

app.add_exception_handler(Exception, global_exception_handler)

# Include API routers
from app.routers import amadeus_content, flightapi_content, openweathermap_content, tomorrowio_content, geoapify_content, googleplaces_content, health
app.include_router(hotelbeds_router)
app.include_router(amadeus_content.router)
app.include_router(flightapi_content.router)
app.include_router(openweathermap_content.router)
app.include_router(tomorrowio_content.router)
app.include_router(geoapify_content.router)
app.include_router(googleplaces_content.router)
app.include_router(health.router)

# Error handling helper functions
def create_error_response(
    error_message: str,
    error_code: ErrorCode = ErrorCode.PROCESSING_ERROR,
    details: Optional[Dict[str, Any]] = None
) -> ErrorResponse:
    """
    Create standardized error response for API endpoints
    Used throughout the application for consistent error formatting
    """
    return ErrorResponse(
        error=error_message,
        error_code=error_code,
        details=details
    )

def create_websocket_error_message(
    error_message: str,
    error_code: ErrorCode = ErrorCode.WEBSOCKET_ERROR,
    client_id: str = ""
) -> str:
    """
    Create standardized WebSocket error messages in JSON format
    Used for consistent error communication over WebSocket connections
    """
    error_data = {
        "type": "error",
        "error": error_message,
        "error_code": error_code.value,
        "timestamp": datetime.now().isoformat(),
        "client_id": client_id
    }
    return json.dumps(error_data)

def get_vertex_ai_client():
    """Get initialized Vertex AI client"""
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    
    return genai.configure(
        vertexai=True,
        project=project_id,
        location=location,
    )

def cleanup_old_continuations():
    """Remove continuations older than 1 hour"""
    current_time = time.time()
    expired_keys = [
        key for key, timestamp in continuation_timestamps.items()
        if current_time - timestamp > 3600  # 1 hour
    ]
    for key in expired_keys:
        continuation_store.pop(key, None)
        continuation_timestamps.pop(key, None)
    
    if expired_keys:
        logger.info(f"Cleaned up {len(expired_keys)} expired continuations")

# FileProcessor已从services.file_processor导入，无需重复定义

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected")

    def disconnect(self, client_id: str):
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected")

    async def send_personal_message(self, message: str, client_id: str):
        if client_id in self.active_connections:
            try:
                websocket = self.active_connections[client_id]
                if websocket.client_state.name == "CONNECTED":
                    await websocket.send_text(message)
                else:
                    logger.warning(f"WebSocket not connected for client {client_id}")
                    self.disconnect(client_id)
            except WebSocketDisconnect:
                logger.info(f"Client {client_id} disconnected during message send")
                self.disconnect(client_id)
            except Exception as e:
                logger.error(f"Failed to send message to client {client_id}: {e}")
                self.disconnect(client_id)

manager = ConnectionManager()

# Global AI optimization systems
prompt_engine: Optional[AdvancedPromptEngine] = None
multimodal_processor: Optional[MultimodalProcessor] = None
performance_monitor: Optional[ModelPerformanceMonitor] = None

# Global continuation store for managing conversation context
continuation_store: Dict[str, Dict[str, Any]] = {}
continuation_timestamps: Dict[str, float] = {}

class MultimodalRequest(BaseModel):
    message: str
    context: Optional[str] = None
    file_descriptions: Optional[dict] = None

class ChatRequest(BaseModel):
    message: str
    context: Optional[str] = None
    user_preferences: Optional[dict] = None

class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    model: str = "fine-tuned-gemini-travel"
    processing_info: Optional[dict] = None
    is_truncated: Optional[bool] = False
    continuation_id: Optional[str] = None

# Helper function to detect truncated responses
def is_response_truncated(response_text: str) -> bool:
    """
    Detect if a response appears to be truncated
    Analyzes response text for common truncation indicators
    Used to determine if conversation continuation is needed
    """
    if not response_text:
        return False

    # Check for common truncation indicators
    truncation_indicators = [
        # Incomplete sentences
        response_text.endswith(('...', '..', '..')),
        # Ends mid-sentence without proper punctuation
        not response_text.strip().endswith(('.', '!', '?', '"', "'", ')', ']', '}')),
        # Ends with incomplete markdown
        response_text.count('```') % 2 != 0,
        response_text.count('**') % 2 != 0,
        response_text.count('*') % 2 != 0,
        # Ends with incomplete list item
        response_text.strip().endswith(('*', '-', '1.', '2.', '3.', '4.', '5.')),
        # Very long response that might hit token limit
        len(response_text) > 3500,
    ]

    return any(truncation_indicators)

# Note: continuation_store and continuation_timestamps are now defined globally above

# cleanup mechanism: Call this periodically or in a background task
def cleanup_old_continuations():
    """Remove continuations older than 1 hour"""
    current_time = time.time()
    expired_keys = [
        key for key, timestamp in continuation_timestamps.items()
        if current_time - timestamp > 3600  # 1 hour
    ]
    for key in expired_keys:
        continuation_store.pop(key, None)
        continuation_timestamps.pop(key, None)

# Initialize Google Cloud Speech client
def initialize_speech_client() -> Optional[Union[SpeechClient, str]]:
    """
    Initialize Google Cloud Speech client with fallback authentication modes
    Supports both API key and service account authentication
    Returns client instance or mode indicator for WebSocket handling
    """
    try:
        # 优先使用API密钥方式
        api_key = os.getenv('GOOGLE_SPEECH_API_KEY')
        if api_key:
            logger.info("Using API key for Speech-to-Text")
            return "api_key_mode"  # 标记使用API密钥模式

        # 尝试使用服务账户凭据
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if credentials_path and os.path.exists(credentials_path):
            credentials = service_account.Credentials.from_service_account_file(credentials_path)
            client = speech.SpeechClient(credentials=credentials)
            logger.info("Speech client initialized with service account credentials")
            return client
        else:
            # 最后尝试默认凭据
            client = speech.SpeechClient()
            logger.info("Speech client initialized with default credentials")
            return client
    except Exception as e:
        logger.error(f"Failed to initialize speech client: {e}")
        return None

speech_client = initialize_speech_client()

# HTTP API方式的语音识别函数
async def recognize_speech_with_api_key(audio_data: bytes) -> dict:
    """
    Perform speech recognition using Google Cloud Speech API with API key authentication
    Processes audio data via HTTP requests for speech-to-text conversion
    Used when service account credentials are not available
    """
    try:
        import base64
        import aiohttp

        api_key = os.getenv('GOOGLE_SPEECH_API_KEY')
        if not api_key:
            raise ValueError("API key not found")

        # 准备请求数据
        audio_base64 = base64.b64encode(audio_data).decode('utf-8')

        request_data = {
            "config": {
                "encoding": "LINEAR16",
                "sampleRateHertz": 16000,
                "languageCode": "en-US",
                "alternativeLanguageCodes": ["es-ES", "fr-FR", "de-DE", "it-IT", "pt-PT"],
                "enableAutomaticPunctuation": True,
                "model": "latest_long"
            },
            "audio": {
                "content": audio_base64
            }
        }

        # 发送HTTP请求
        url = f"https://speech.googleapis.com/v1/speech:recognize?key={api_key}"

        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=request_data) as response:
                if response.status == 200:
                    result = await response.json()
                    return result
                else:
                    error_text = await response.text()
                    logger.error(f"Speech API error {response.status}: {error_text}")
                    return {"error": f"API error {response.status}"}

    except Exception as e:
        logger.error(f"Speech recognition with API key failed: {e}")
        return {"error": str(e)}

# API密钥模式的WebSocket处理函数
async def handle_speech_with_api_key(websocket: WebSocket, client_id: str):
    """
    Handle WebSocket speech recognition using API key authentication
    Processes real-time audio streams and returns transcribed text
    Fallback method when service account streaming is unavailable
    """
    try:
        audio_buffer = bytearray()
        if len(audio_buffer) > MAX_AUDIO_BUFFER_SIZE:
            # Clear buffer or process in chunks
            audio_buffer = audio.buffer[-MAX_AUDIO_BUFFER_SIZE//2:] # Keep last half

        last_recognition_time = time.time()
        recognition_interval = 3.0  # 每3秒识别一次

        while True:
            try:
                # 接收音频数据
                data = await asyncio.wait_for(websocket.receive_bytes(), timeout=0.1)

                if len(data) > 0:
                    audio_buffer.extend(data)

                # 检查是否需要进行识别
                current_time = time.time()
                if (current_time - last_recognition_time >= recognition_interval and
                    len(audio_buffer) > 16000):  # 至少1秒的音频数据

                    # 进行语音识别
                    audio_data = bytes(audio_buffer)
                    result = await recognize_speech_with_api_key(audio_data)

                    if "results" in result and result["results"]:
                        for speech_result in result["results"]:
                            if "alternatives" in speech_result and speech_result["alternatives"]:
                                transcript = speech_result["alternatives"][0].get("transcript", "")
                                confidence = speech_result["alternatives"][0].get("confidence", 0.0)

                                if transcript.strip():
                                    # 发送识别结果
                                    await manager.send_personal_message(json.dumps({
                                        "type": "transcript",
                                        "transcript": transcript,
                                        "is_final": True,
                                        "confidence": confidence,
                                        "detected_language": "en-US"
                                    }), client_id)

                                    logger.info(f"API Key recognition for {client_id}: {transcript}")

                                    # 处理AI响应
                                    if gemini_model:
                                        try:
                                            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {transcript}"
                                            model_name = os.getenv("VERTEX_AI_ENDPOINT")

                                            contents = [
                                                genai.types.Content(
                                                    role="user",
                                                    parts=[genai.types.Part(text=full_prompt)]
                                                )
                                            ]

                                            config = genai.types.GenerateContentConfig(
                                                temperature=0.7,
                                                top_p=0.9,
                                                max_output_tokens=2048,
                                            )

                                            client = get_vertex_ai_client()
                                            response = client.models.generate_content(
                                                model=model_name,
                                                contents=contents,
                                                config=config
                                            )

                                            ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response."

                                            await manager.send_personal_message(json.dumps({
                                                "type": "response",
                                                "response": ai_response,
                                                "timestamp": datetime.now().isoformat()
                                            }), client_id)
                                        except Exception as e:
                                            logger.error(f"Gemini processing error: {e}")

                    # 清空缓冲区并更新时间
                    audio_buffer.clear()
                    last_recognition_time = current_time

            except asyncio.TimeoutError:
                # 超时是正常的，继续循环
                continue
            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for client {client_id}")
                break

    except Exception as e:
        logger.error(f"API key speech handling error: {e}")
        if websocket.client_state.name == "CONNECTED":
            await manager.send_personal_message(json.dumps({
                "type": "error",
                "error": f"Speech recognition failed: {str(e)}"
            }), client_id)

# 流式处理模式的WebSocket处理函数
async def handle_speech_with_streaming(websocket: WebSocket, client_id: str, speech_client):
    """
    Handle WebSocket speech recognition using streaming service account authentication
    Provides real-time bidirectional streaming for optimal speech-to-text performance
    Primary method for production speech recognition when credentials are available
    """
    try:
        # Configure speech recognition
        config = speech.RecognitionConfig(
            encoding=speech.RecognitionConfig.AudioEncoding.LINEAR16,
            sample_rate_hertz=16000,
            language_code="en-US",
            alternative_language_codes=["es-ES", "fr-FR", "de-DE", "it-IT", "pt-PT"],
            enable_automatic_punctuation=True,
            enable_word_time_offsets=False,
            model="latest_long"
        )

        streaming_config = speech.StreamingRecognitionConfig(
            config=config,
            interim_results=True,
            single_utterance=False
        )

        async def audio_stream():
            try:
                while True:
                    data = await websocket.receive_bytes()

                    if len(data) == 0:
                        logger.warning("Received empty audio data")
                        continue
                    if len(data) % 2 != 0:
                        logger.warning(f"Invalid audio data length (not even): {len(data)}")
                        continue
                    if len(data) > 1024 * 1024:
                        logger.warning(f"Audio chunk too large: {len(data)} bytes")
                        continue
                    yield data

            except WebSocketDisconnect:
                logger.info(f"WebSocket disconnected for client {client_id}")
                return
            except Exception as e:
                logger.error(f"Error in audio stream: {e}")
                return

        # Start streaming recognition (基于Google官方示例)
        def request_generator():
            # 第一个请求必须只包含配置，不包含音频数据
            yield speech.StreamingRecognizeRequest(streaming_config=streaming_config)

            # 后续请求包含音频数据
            for audio_data in audio_stream():
                if audio_data and len(audio_data) > 0:  # 确保数据不为空且有内容
                    yield speech.StreamingRecognizeRequest(audio_content=audio_data)

        # Call streaming API with generator (正确的API调用方式)
        responses = speech_client.streaming_recognize(
            config=streaming_config,
            requests=request_generator()
        )

        # Process responses in real-time
        async for response in responses:
            for result in response.results:
                transcript = result.alternatives[0].transcript
                is_final = result.is_final
                detected_language = result.language_code if hasattr(result, 'language_code') else "en-US"

                await manager.send_personal_message(json.dumps({
                    "type": "transcript",
                    "transcript": transcript,
                    "is_final": is_final,
                    "confidence": result.alternatives[0].confidence if result.alternatives else 0.0,
                    "detected_language": detected_language
                }), client_id)

                if is_final:
                    logger.info(f"Final transcript received for {client_id}: {transcript}")

                    # Process transcript with Gemini
                    if gemini_model:
                        try:
                            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {transcript}"
                            model_name = os.getenv("VERTEX_AI_ENDPOINT")

                            contents = [
                                genai.types.Content(
                                    role="user",
                                    parts=[genai.types.Part(text=full_prompt)]
                                )
                            ]

                            config = genai.types.GenerateContentConfig(
                                temperature=0.7,
                                top_p=0.9,
                                max_output_tokens=2048,
                            )
                            
                            client = get_vertex_ai_client()
                            response = client.models.generate_content(
                                model=model_name,
                                contents=contents,
                                config=config
                            )

                            ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response. Please try again."

                            await manager.send_personal_message(json.dumps({
                                "type": "response",
                                "response": ai_response,
                                "timestamp": datetime.now().isoformat()
                            }), client_id)
                        except Exception as e:
                            logger.error(f"Gemini processing error: {e}")
                            await manager.send_personal_message(json.dumps({
                                "type": "error",
                                "error": f"Chat processing error: {str(e)}"
                            }), client_id)

    except Exception as e:
        logger.error(f"Streaming recognition error: {e}")
        if websocket.client_state.name == "CONNECTED":
            await manager.send_personal_message(json.dumps({
                "type": "error",
                "error": f"Speech recognition failed: {str(e)}"
            }), client_id)

def get_vertex_ai_client() -> genai.Client:
    """Get initialized Vertex AI client"""
    project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
    location = os.getenv("GOOGLE_CLOUD_LOCATION")
    
    if not project_id:
        raise ValueError("GOOGLE_CLOUD_PROJECT environment variable is required")
    if not location:
        raise ValueError("GOOGLE_CLOUD_LOCATION environment variable is required")
    
    # Initialize Vertex AI client
    client = genai.Client(
        vertexai=True,
        project=project_id,
        location=location
    )
    
    return client

# Initialize Google Gen AI SDK
def initialize_genai() -> Optional[genai.Client]:
    """
    Initialize Google Generative AI SDK client
    Sets up authentication and validates API connectivity
    Central initialization point for all AI model interactions
    """
    try:
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not project_id:
            logger.error("Missing required env var: GOOGLE_CLOUD_PROJECT")

        location = os.getenv("GOOGLE_CLOUD_LOCATION")
        if not location:
            logger.error("Missing required env var: GOOGLE_CLOUD_LOCATION")

        model_name = os.getenv("VERTEX_AI_ENDPOINT")
        if not model_name:
            logger.error("Missing required env var: VERTEX_AI_ENDPOINT")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )
        logger.info("Vertex AI 初始化成功")

        return client

    except Exception as e:
        logger.error(f"Failed to initialize Google Gen AI: {e}")
        return None

# Initialize the global model
gemini_model = None

# Initialize on startup
@app.on_event("startup")
async def startup_event():
    global gemini_model, prompt_engine, multimodal_processor, performance_monitor

    # Phase 1 Fix: API Key Validation at startup
    logger.info("Starting SynTour API with enhanced AI capabilities...")

    try:
        # Validate all API keys
        api_results = await api_validator.validate_all_apis()
        failed_apis = [api for api, status in api_results.items() if not status]

        if failed_apis:
            logger.warning(f"Some APIs failed validation: {failed_apis}")
        else:
            logger.info("All APIs validated successfully")
    except Exception as e:
        logger.error(f"API validation failed: {e}")

    # Initialize Gemini model
    gemini_model = initialize_genai()

    if gemini_model:
        logger.info("Gemini model initialized successfully")
    else:
        logger.warning("Failed to initialize fine-tuned Gemini model")

    # Initialize AI optimization systems
    try:
        prompt_engine = AdvancedPromptEngine()
        multimodal_processor = MultimodalProcessor()
        performance_monitor = ModelPerformanceMonitor()
        logger.info("AI optimization systems initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize AI optimization systems: {e}")

    logger.info("SynTour API started successfully with enhanced AI capabilities")

# Health check endpoints
@app.get("/")
async def root():
    """
    Root endpoint providing API status and feature information
    Returns basic health status and available features
    Used for service discovery and health monitoring
    """
    return {
        "message": "SynTour AI API Enhanced - Running",
        "status": "healthy",
        "version": "2.1.0",
        "features": [
            "enhanced_prompt_engineering",
            "multimodal_processing",
            "performance_monitoring",
            "travel_planning",
            "file_upload",
            "speech_recognition"
        ],
        "ai_systems": {
            "prompt_engine": bool(prompt_engine),
            "multimodal_processor": bool(multimodal_processor),
            "performance_monitor": bool(performance_monitor)
        }
    }

@app.get("/health")
async def health_check():
    """
    Comprehensive health check endpoint
    Validates all critical services and API connections
    Essential for load balancer health checks and monitoring
    """
    genai_status = "connected" if initialize_genai() else "disconnected"
    return {
        "status": "healthy",
        "service": "fastapi-backend-enhanced",
        "genai": genai_status,
        "ai_systems": {
            "prompt_engine": "active" if prompt_engine else "inactive",
            "multimodal_processor": "active" if multimodal_processor else "inactive",
            "performance_monitor": "active" if performance_monitor else "inactive"
        }
    }

@app.get("/api/ai/performance")
async def get_performance_metrics():
    """
    Get AI model performance metrics and health status
    Provides insights into system performance and optimization opportunities
    """
    try:
        if not performance_monitor:
            return {
                "error": "Performance monitoring not available",
                "status": "disabled"
            }

        # Get performance summary
        summary = performance_monitor.get_performance_summary(hours=24)

        return {
            "success": True,
            "performance_summary": summary,
            "monitoring_status": "active",
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"Performance metrics error: {e}")
        return {
            "success": False,
            "error": f"Failed to retrieve performance metrics: {str(e)}",
            "timestamp": datetime.now().isoformat()
        }

# Test endpoint for Vertex AI Gemini
@app.post("/test-vertex")
async def test_vertex_endpoint(request: ChatRequest):
    try:
        logger.info(f"Test Vertex AI request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client using helper function
        client = get_vertex_ai_client()

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Create prompt
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=2048,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Test Vertex AI response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini-test",
            processing_info={"prompt_type": "test", "endpoint": "vertex"}
        )

    except Exception as e:
        logger.error(f"Test Vertex AI error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Test failed: {str(e)}",
            model="fine-tuned-gemini-test"
        )

# Simple chat endpoint (following malaysia-ai-backend pattern)
@app.post("/chat", response_model=ChatResponse)
async def simple_chat(request: ChatRequest):
    try:
        logger.info(f"Simple chat request: {request.message[:50]}...")

        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client using helper function
        client = get_vertex_ai_client()

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # 3. Simple prompt without complex templates
        prompt = f"You are a helpful travel assistant. User: {request.message}\n\nAssistant:"

        # 4. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 5. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 6. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        logger.info(f"Simple chat response generated successfully")

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "simple", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Simple chat error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to generate response: {str(e)}",
            model="fine-tuned-gemini"
        )

# Enhanced AI Chat endpoint with prompt engineering
@app.post("/api/ai/chat", response_model=ChatResponse)
async def chat_with_ai(request: ChatRequest):
    """
    Enhanced AI chat endpoint with advanced prompt engineering
    Provides intelligent travel planning and general conversation capabilities
    Features context-aware responses and travel-specific optimizations
    """
    start_time = time.time()

    try:
        # Input validation
        if not request.message or not request.message.strip():
            return create_error_response(
                "Message cannot be empty",
                ErrorCode.VALIDATION_ERROR
            )

        message = request.message.strip()
        context_data = request.context or ""
        preferences_dict = request.user_preferences or {}

        logger.info(f"Enhanced AI chat request: {message[:100]}...")

        # Use advanced prompt engineering
        if not prompt_engine:
            logger.warning("Prompt engine not initialized, falling back to basic prompt")
            full_prompt = message
        else:
            # Determine prompt type intelligently
            prompt_type = prompt_engine.determine_prompt_type(message, {
                "context": context_data,
                "preferences": preferences_dict
            })

            # Generate enhanced prompt
            prompt_data = prompt_engine.generate_enhanced_prompt(
                prompt_type=prompt_type,
                user_message=message,
                context={
                    "general_context": context_data,
                    "user_preferences": preferences_dict
                },
                user_preferences=preferences_dict
            )

            full_prompt = prompt_data["user_prompt"]
            system_prompt = prompt_data["system_prompt"]
            model_params = prompt_data["parameters"]

            logger.info(f"Using prompt type: {prompt_type.value}")

        # Prepare context for AI processing
        processing_context = {
            "request_type": "chat",
            "prompt_type": prompt_type.value if prompt_engine else "basic",
            "has_context": bool(context_data),
            "has_preferences": bool(preferences_dict)
        }

        # Process with enhanced AI system
        try:
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
            location = os.getenv("GOOGLE_CLOUD_LOCATION")

            client = genai.Client(
                vertexai=True,
                project=project_id,
                location=location,
            )

            model_name = os.getenv("VERTEX_AI_ENDPOINT")

            # Create content with system prompt if available
            content_parts = []
            if prompt_engine and 'system_prompt' in locals():
                # Add system prompt as context
                content_parts.append(genai.types.Part(text=f"System: {system_prompt}\n\nUser: {full_prompt}"))
            else:
                content_parts.append(genai.types.Part(text=full_prompt))

            contents = [
                genai.types.Content(
                    role="user",
                    parts=content_parts
                )
            ]

            # Use optimized model parameters if available
            if prompt_engine and 'model_params' in locals():
                config = genai.types.GenerateContentConfig(
                    temperature=model_params.get("temperature", 0.7),
                    top_p=model_params.get("top_p", 0.9),
                    max_output_tokens=model_params.get("max_tokens", 2048),
                )
            else:
                config = genai.types.GenerateContentConfig(
                    temperature=0.7,
                    top_p=0.9,
                    max_output_tokens=2048,
                )

            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=config
            )

            processing_time = time.time() - start_time

            if response and response.text:
                # Track performance metrics
                if performance_monitor:
                    await performance_monitor.track_model_performance(
                        request_data=processing_context,
                        response_data={
                            "success": True,
                            "response": response.text,
                            "token_count": len(response.text.split()),  # Approximate
                            "prompt_type": prompt_type.value if prompt_engine else "basic",
                            "cache_hit": False
                        },
                        processing_time=processing_time
                    )

                return ChatResponse(
                    success=True,
                    response=response.text,
                    model="fine-tuned-gemini-travel",
                    processing_info={
                        "processing_time": processing_time,
                        "prompt_type": prompt_type.value if prompt_engine else "basic",
                        "enhanced_processing": bool(prompt_engine)
                    }
                )
            else:
                return create_error_response(
                    "No response generated from AI model",
                    ErrorCode.PROCESSING_ERROR
                )

        except Exception as ai_error:
            processing_time = time.time() - start_time
            logger.error(f"Enhanced AI processing error: {ai_error}")

            # Track error metrics
            if performance_monitor:
                await performance_monitor.track_model_performance(
                    request_data=processing_context,
                    response_data={
                        "success": False,
                        "error": str(ai_error),
                        "error_code": "AI_PROCESSING_ERROR"
                    },
                    processing_time=processing_time
                )

            return create_error_response(
                f"Enhanced AI service error: {str(ai_error)}",
                ErrorCode.PROCESSING_ERROR
            )
        
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        return create_error_response(
            f"Internal server error: {str(e)}",
            ErrorCode.PROCESSING_ERROR
        )


# Enhanced Multimodal AI endpoint
@app.post("/api/ai/multimodal", response_model=ChatResponse)
async def multimodal_ai_chat(
    message: str = Form(...),
    context: Optional[str] = Form(None),
    files: List[UploadFile] = File(None)
):
    """
    Advanced multimodal AI endpoint with enhanced processing capabilities
    Supports text, images, and file uploads with intelligent analysis
    Features travel-specific visual recognition and content understanding
    """
    start_time = time.time()

    try:
        logger.info(f"Enhanced multimodal request: {message[:100]}... with {len(files) if files else 0} files")

        # Process files and extract content
        processed_images = []
        processed_files = []

        if files:
            for file in files:
                file_content = await file.read()
                file_info = {
                    "name": file.filename,
                    "type": file.content_type,
                    "size": len(file_content),
                    "content": file_content
                }

                # Categorize by type
                if file.content_type and file.content_type.startswith('image/'):
                    processed_images.append(file_content)
                else:
                    processed_files.append(file_info)

        # Use enhanced multimodal processing
        multimodal_analysis = None
        if multimodal_processor:
            try:
                multimodal_analysis = await multimodal_processor.process_multimodal_input(
                    text_input=message,
                    images=processed_images if processed_images else None,
                    files=processed_files if processed_files else None,
                    context={"general_context": context} if context else None
                )
                logger.info("Multimodal analysis completed successfully")
            except Exception as e:
                logger.error(f"Multimodal analysis failed: {e}")

        # Generate enhanced prompt
        if prompt_engine and multimodal_analysis:
            # Use multimodal prompt type
            prompt_data = prompt_engine.generate_enhanced_prompt(
                prompt_type=PromptType.MULTIMODAL_ANALYSIS,
                user_message=message,
                context={
                    "image_description": multimodal_analysis.get("integrated_insights", {}).get("visual_insights", []),
                    "user_context": context or "No additional context",
                    "multimodal_analysis": multimodal_analysis
                }
            )

            enhanced_prompt = prompt_data["user_prompt"]
            system_prompt = prompt_data["system_prompt"]
            model_params = prompt_data["parameters"]
        else:
            # Fallback to basic multimodal prompt
            if processed_images or processed_files:
                enhanced_prompt = f"Multimodal travel query with {len(processed_images)} images and {len(processed_files)} files: {message}"
            else:
                enhanced_prompt = message
            model_params = {"temperature": 0.7, "top_p": 0.9, "max_tokens": 4096}

        # Process with AI model
        try:
            project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
            location = os.getenv("GOOGLE_CLOUD_LOCATION")

            client = genai.Client(
                vertexai=True,
                project=project_id,
                location=location,
            )

            model_name = os.getenv("VERTEX_AI_ENDPOINT")

            # Create enhanced content
            content_parts = []
            if prompt_engine and 'system_prompt' in locals():
                content_parts.append(genai.types.Part(text=f"System: {system_prompt}\n\nUser: {enhanced_prompt}"))
            else:
                content_parts.append(genai.types.Part(text=enhanced_prompt))

            contents = [
                genai.types.Content(
                    role="user",
                    parts=content_parts
                )
            ]

            # Use optimized configuration
            config = genai.types.GenerateContentConfig(
                temperature=model_params.get("temperature", 0.7),
                top_p=model_params.get("top_p", 0.9),
                max_output_tokens=model_params.get("max_tokens", 4096),
            )

            response = client.models.generate_content(
                model=model_name,
                contents=contents,
                config=config,
            )

            processing_time = time.time() - start_time
            ai_response = response.text if response.text else "No response generated"

            # Track performance
            if performance_monitor:
                await performance_monitor.track_model_performance(
                    request_data={
                        "request_type": "multimodal",
                        "has_images": bool(processed_images),
                        "has_files": bool(processed_files),
                        "enhanced_processing": bool(multimodal_processor)
                    },
                    response_data={
                        "success": True,
                        "response": ai_response,
                        "token_count": len(ai_response.split()),
                        "prompt_type": "multimodal_analysis",
                        "multimodal_analysis": bool(multimodal_analysis)
                    },
                    processing_time=processing_time
                )

            return ChatResponse(
                success=True,
                response=ai_response,
                model="fine-tuned-gemini-multimodal",
                processing_info={
                    "processing_time": processing_time,
                    "prompt_type": "multimodal_analysis",
                    "images_processed": len(processed_images),
                    "files_processed": len(processed_files),
                    "enhanced_processing": bool(multimodal_processor),
                    "analysis_insights": len(multimodal_analysis.get("integrated_insights", {}).get("visual_insights", [])) if multimodal_analysis else 0
                }
            )

        except Exception as ai_error:
            processing_time = time.time() - start_time
            logger.error(f"Enhanced multimodal AI error: {ai_error}")

            # Track error
            if performance_monitor:
                await performance_monitor.track_model_performance(
                    request_data={
                        "request_type": "multimodal",
                        "has_images": bool(processed_images),
                        "has_files": bool(processed_files)
                    },
                    response_data={
                        "success": False,
                        "error": str(ai_error),
                        "error_code": "MULTIMODAL_AI_ERROR"
                    },
                    processing_time=processing_time
                )

            return ChatResponse(
                success=False,
                error=f"Enhanced multimodal processing failed: {str(ai_error)}",
                model="fine-tuned-gemini-multimodal"
            )

    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Multimodal endpoint error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Multimodal request processing failed: {str(e)}",
            processing_info={"processing_time": processing_time}
        )


# Specialized travel planning endpoint
@app.post("/api/travel/plan", response_model=ChatResponse)
async def create_travel_plan(request: ChatRequest):
    """
    Specialized travel planning endpoint with expert travel knowledge
    Creates comprehensive travel itineraries with detailed recommendations
    Optimized for complex multi-destination travel planning scenarios
    """
    try:
        # --- CORRECTED CODE using Google Gen AI SDK ---

        # 1. Initialize the client
        project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        location = os.getenv("GOOGLE_CLOUD_LOCATION")

        client = genai.Client(
            vertexai=True,
            project=project_id,
            location=location,
        )

        # 2. Define your fine-tuned model's full resource name
        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Extract travel parameters from user preferences
        duration = None
        budget = None
        interests = None
        travelers = None

        if request.user_preferences:
            duration = request.user_preferences.get("duration")
            budget = request.user_preferences.get("budget")
            interests = request.user_preferences.get("interests")
            travelers = request.user_preferences.get("travelers")

        # Use specialized travel planning prompt
        prompt = PromptTemplates.travel_planning_prompt(
            user_message=request.message,
            duration=duration,
            budget=budget,
            interests=interests,
            travelers=travelers
        )

        # 3. Create content and config
        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        # 4. Generate content using the correct API
        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        # 5. Extract the response text
        ai_response = response.text if response.text else "No response generated"

        # --- END CORRECTED CODE ---

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "specialized_travel_planning"}
        )

    except Exception as e:
        logger.error(f"Travel planning error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to create travel plan: {str(e)}"
        )


# Continuation endpoint for truncated responses
@app.post("/api/ai/continue/{continuation_id}", response_model=ChatResponse)
async def continue_response(continuation_id: str):
    """
    Continue truncated AI responses using stored conversation context
    Seamlessly resumes interrupted conversations without losing context
    Essential for handling long-form travel planning responses
    """
    try:
        # Check if continuation context exists
        if continuation_id not in continuation_store:
            return ChatResponse(
                success=False,
                error="Continuation context not found or expired"
            )

        context = continuation_store[continuation_id]

        # Check if context is not too old (1 hour limit)
        if time.time() - context["timestamp"] > 3600:
            del continuation_store[continuation_id]
            return ChatResponse(
                success=False,
                error="Continuation context has expired"
            )

        # Initialize the client using helper function
        client = get_vertex_ai_client()

        model_name = os.getenv("VERTEX_AI_ENDPOINT")

        # Create continuation prompt
        continuation_prompt = f"""Please continue the following response where it was cut off:

Original question: {context['original_message']}

Previous response (incomplete):
{context['partial_response']}

Please continue from where the response was cut off, maintaining the same tone and style:"""

        contents = [
            genai.types.Content(
                role="user",
                parts=[genai.types.Part(text=continuation_prompt)]
            )
        ]

        config = genai.types.GenerateContentConfig(
            temperature=0.7,
            top_p=0.9,
            max_output_tokens=4096,
        )

        response = client.models.generate_content(
            model=model_name,
            contents=contents,
            config=config,
        )

        ai_response = response.text if response.text else "No continuation generated"

        # Clean up the continuation context
        del continuation_store[continuation_id]

        return ChatResponse(
            success=True,
            response=ai_response,
            model="fine-tuned-gemini",
            processing_info={"prompt_type": "continuation", "endpoint": "custom"}
        )

    except Exception as e:
        logger.error(f"Continuation error: {str(e)}")
        return ChatResponse(
            success=False,
            error=f"Failed to continue response: {str(e)}"
        )


# 候选语言列表（可扩展）
CANDIDATE_LANGUAGES = [
    "en-US", "zh-CN", "ja-JP", "fr-FR", "es-ES",
    "de-DE", "ko-KR", "ru-RU", "it-IT", "pt-BR",
    "id-ID", "ms-MY", "ta-IN", "ta-MY", "th-TH",
    "tr-TR", "uk-UA", "uz-UZ", "vi-VN", "sv-SE"
]

@app.websocket("/ws/speech/{client_id}")
async def websocket_speech_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)

    try:
        # Timeout for WebSocket operations
        async with asyncio.timeout(300):

            if not speech_client:
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "error": "Speech recognition service not available"
                }))
                await websocket.close()
                return

            # 检查是否使用API密钥模式
            use_api_key = speech_client == "api_key_mode"

            try:
                if use_api_key:
                    # API密钥模式：收集音频数据并批量处理
                    await handle_speech_with_api_key(websocket, client_id)
                else:
                    # 服务账户模式：使用流式处理
                    await handle_speech_with_streaming(websocket, client_id, speech_client)

            except WebSocketDisconnect:
                logger.info(f"Client {client_id} disconnected")
                manager.disconnect(client_id)
            except Exception as e:
                logger.error(f"WebSocket speech endpoint error: {e}")
                if websocket.client_state.name == "CONNECTED":
                    await manager.send_personal_message(json.dumps({
                        "type": "error",
                        "error": f"Speech recognition failed: {str(e)}"
                    }), client_id)
            finally:
                try:
                    # 检查WebSocket状态并安全关闭
                    if websocket.client_state.name not in ["DISCONNECTED", "CLOSED"]:
                        await websocket.close(code=1000, reason="Normal closure")
                        logger.info(f"WebSocket closed for client {client_id}")
                except Exception as e:
                    logger.error(f"Error closing WebSocket for client {client_id}: {e}")
                finally:
                    # 确保连接从管理器中移除
                    manager.disconnect(client_id)

    except asyncio.TimeoutError:
        logger.warning(f"WebSocket timeout for client {client_id}")
        await websocket.close(code=1008, reason="Timeout")

@app.websocket("/ws/chat/{client_id}")
async def websocket_chat_endpoint(websocket: WebSocket, client_id: str):
    await manager.connect(websocket, client_id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()

            # 添加JSON验证
            try:
                message_data = json.loads(data)
                if not isinstance(message_data, dict) or "message" not in message_data:
                    raise ValueError("Invalid message format")
            except (json.JSONDecodeError, ValueError) as e:
                error_msg = create_websocket_error_message(
                    f"Invalid message format: {str(e)}",
                    ErrorCode.VALIDATION_ERROR,
                    client_id
                )
                await manager.send_personal_message(error_msg, client_id)
                continue
            
            if not gemini_model:
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "error": "Chat service not available"
                }), client_id)
                continue
            
            # Process the message
            user_message = message_data.get("message", "")
            full_prompt = f"{TOURISM_PROMPT}\n\nUser: {user_message}"
            
            try:
                model_name = os.getenv("VERTEX_AI_ENDPOINT")
                response = gemini_model.models.generate_content(
                    model=model_name,
                    contents=full_prompt
                )
                
                ai_response = response.text if hasattr(response, 'text') and response.text else "I'm sorry, I couldn't generate a response. Please try again."

                await manager.send_personal_message(json.dumps({
                    "type": "response",
                    "response": ai_response.text,
                    "timestamp": datetime.now().isoformat()
                }), client_id)
                
            except Exception as e:
                logger.error(f"Chat generation error: {e}")
                await manager.send_personal_message(json.dumps({
                    "type": "error",
                    "error": f"Failed to generate response: {str(e)}",
                    "timestamp": datetime.now().isoformat()
                }), client_id)
    
    except WebSocketDisconnect:
        logger.info(f"Chat client {client_id} disconnected")
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Chat WebSocket error for client {client_id}: {e}")
    finally:
        try:
            # 检查WebSocket状态并安全关闭
            if websocket.client_state.name not in ["DISCONNECTED", "CLOSED"]:
                await websocket.close(code=1000, reason="Normal closure")
                logger.info(f"Chat WebSocket closed for client {client_id}")
        except Exception as e:
            logger.error(f"Error closing chat WebSocket for client {client_id}: {e}")
        finally:
            # 确保连接从管理器中移除
            manager.disconnect(client_id)




if __name__ == "__main__":
    import uvicorn
    uvicorn.run("main:app", host="0.0.0.0", port=8000, reload=True)
