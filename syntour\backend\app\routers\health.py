# app/routers/health.py
from fastapi import APIRouter, HTTPException
from typing import Dict, Any
import asyncio
import time
from datetime import datetime

from app.core.api_validator import api_validator
from app.core.cache_manager import cache_manager

router = APIRouter(prefix="/health", tags=["Health"])

@router.get("/")
async def health_check():
    """Basic health check"""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat() + "Z",
        "service": "SynTour API"
    }

@router.get("/detailed")
async def detailed_health_check():
    """Detailed health check with API status"""
    start_time = time.time()
    
    try:
        # Check API validations (with timeout)
        api_status = {}
        try:
            api_results = await asyncio.wait_for(
                api_validator.validate_all_apis(),
                timeout=10.0
            )
            api_status = api_results
        except asyncio.TimeoutError:
            api_status = {"error": "API validation timeout"}
        
        # Get cache statistics
        cache_stats = await cache_manager.get_cache_stats()
        
        response_time = time.time() - start_time
        
        # Determine overall health
        healthy_apis = sum(1 for status in api_status.values() if status is True)
        total_apis = len(api_status)
        health_percentage = (healthy_apis / total_apis * 100) if total_apis > 0 else 0
        
        overall_status = "healthy" if health_percentage >= 80 else "degraded" if health_percentage >= 50 else "unhealthy"
        
        return {
            "status": overall_status,
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "response_time_ms": round(response_time * 1000, 2),
            "api_status": api_status,
            "cache_stats": cache_stats,
            "health_percentage": round(health_percentage, 1),
            "healthy_apis": f"{healthy_apis}/{total_apis}"
        }
        
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e)
        }

@router.get("/apis/{api_name}")
async def api_specific_health(api_name: str):
    """Health check for specific API"""
    
    valid_apis = [
        "amadeus", "hotelbeds", "geoapify", "google_places",
        "openweather", "tomorrow_io", "flight_api"
    ]
    
    if api_name not in valid_apis:
        raise HTTPException(
            status_code=404,
            detail=f"API '{api_name}' not found. Valid APIs: {', '.join(valid_apis)}"
        )
    
    try:
        # Validate specific API
        validation_method = getattr(api_validator, f'_validate_{api_name}', None)
        if not validation_method:
            raise HTTPException(
                status_code=500,
                detail=f"Validation method not found for {api_name}"
            )
        
        start_time = time.time()
        is_healthy = await validation_method()
        response_time = time.time() - start_time
        
        return {
            "api_name": api_name,
            "status": "healthy" if is_healthy else "unhealthy",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "response_time_ms": round(response_time * 1000, 2),
            "healthy": is_healthy
        }
        
    except Exception as e:
        return {
            "api_name": api_name,
            "status": "error",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e),
            "healthy": False
        }

@router.get("/cache")
async def cache_statistics():
    """Get detailed cache statistics"""
    try:
        stats = await cache_manager.get_cache_stats()
        
        return {
            "status": "success",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "cache_statistics": stats,
            "cache_efficiency": {
                "hit_rate": round((stats["total_hits"] / max(stats["total_entries"], 1)) * 100, 2),
                "utilization": round((stats["cache_size"] / stats["max_size"]) * 100, 2),
                "expired_percentage": round((stats["expired_entries"] / max(stats["total_entries"], 1)) * 100, 2)
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e)
        }

@router.post("/cache/clear")
async def clear_cache():
    """Clear all cache entries"""
    try:
        await cache_manager.cache.clear()
        return {
            "status": "success",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "message": "Cache cleared successfully"
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e)
        }

@router.post("/cache/invalidate/{api_name}")
async def invalidate_api_cache(api_name: str):
    """Invalidate cache for specific API"""
    try:
        count = await cache_manager.invalidate_cache(api_name)
        return {
            "status": "success",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "api_name": api_name,
            "invalidated_entries": count,
            "message": f"Invalidated {count} cache entries for {api_name}"
        }
    except Exception as e:
        return {
            "status": "error",
            "timestamp": datetime.utcnow().isoformat() + "Z",
            "error": str(e)
        }