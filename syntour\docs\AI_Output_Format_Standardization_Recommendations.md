# SynTour AI输出格式标准化建议

## 概述
基于对SynTour AI系统输出格式的分析，本文档提供统一的AI响应格式规范、版本控制机制和向后兼容性策略，以提升前后端数据交互的一致性和可维护性。

## 1. 当前输出格式分析

### 1.1 现有格式问题
**当前响应格式:**
```python
# main.py 中的不一致格式
class ChatResponse(BaseModel):
    success: bool
    response: Optional[str] = None
    error: Optional[str] = None
    model: str = "fine-tuned-gemini-travel"
    processing_info: Optional[dict] = None
    is_truncated: Optional[bool] = False
    continuation_id: Optional[str] = None
```

**问题识别:**
- 字段命名不一致（response vs data）
- 缺乏统一的元数据结构
- 没有版本控制机制
- 错误信息格式不标准
- 缺乏响应类型分类

### 1.2 格式不一致示例
```python
# 不同端点返回不同格式
# /api/ai/chat
{"success": true, "response": "...", "model": "..."}

# /api/amadeus/flight-offers
{"data": [...], "meta": {...}}

# /health
{"status": "healthy", "timestamp": "..."}
```

## 2. 统一响应格式规范

### 2.1 标准响应结构
**优化建议:**
```python
# 新建 app/core/response_formatter.py
from typing import Any, Dict, List, Optional, Union
from pydantic import BaseModel, Field
from datetime import datetime
from enum import Enum
import uuid

class ResponseStatus(str, Enum):
    SUCCESS = "success"
    ERROR = "error"
    WARNING = "warning"
    PARTIAL = "partial"

class ResponseType(str, Enum):
    AI_CHAT = "ai_chat"
    API_DATA = "api_data"
    TRAVEL_PLAN = "travel_plan"
    HEALTH_CHECK = "health_check"
    VALIDATION = "validation"
    SYSTEM = "system"

class ErrorCode(str, Enum):
    VALIDATION_ERROR = "VALIDATION_ERROR"
    API_ERROR = "API_ERROR"
    PROCESSING_ERROR = "PROCESSING_ERROR"
    AUTHENTICATION_ERROR = "AUTHENTICATION_ERROR"
    RATE_LIMIT_ERROR = "RATE_LIMIT_ERROR"
    TIMEOUT_ERROR = "TIMEOUT_ERROR"
    INTERNAL_ERROR = "INTERNAL_ERROR"

class ResponseMetadata(BaseModel):
    """响应元数据"""
    request_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = Field(default_factory=datetime.now)
    version: str = "1.0"
    response_type: ResponseType
    processing_time_ms: Optional[float] = None
    source: Optional[str] = None
    cache_hit: Optional[bool] = None

class ErrorDetail(BaseModel):
    """错误详情"""
    code: ErrorCode
    message: str
    field: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    suggestion: Optional[str] = None

class PaginationInfo(BaseModel):
    """分页信息"""
    page: int = 1
    page_size: int = 20
    total_items: Optional[int] = None
    total_pages: Optional[int] = None
    has_next: bool = False
    has_previous: bool = False

class StandardResponse(BaseModel):
    """标准响应格式"""
    status: ResponseStatus
    data: Optional[Any] = None
    errors: List[ErrorDetail] = []
    warnings: List[str] = []
    metadata: ResponseMetadata
    pagination: Optional[PaginationInfo] = None
    
    # AI特定字段
    ai_model: Optional[str] = None
    confidence_score: Optional[float] = None
    is_truncated: bool = False
    continuation_token: Optional[str] = None
    
    # 向后兼容字段
    success: Optional[bool] = None
    message: Optional[str] = None
    
    def __init__(self, **data):
        super().__init__(**data)
        # 自动设置向后兼容字段
        self.success = self.status == ResponseStatus.SUCCESS
        if self.data and isinstance(self.data, str):
            self.message = self.data

class ResponseFormatter:
    """响应格式化器"""
    
    def __init__(self, version: str = "1.0"):
        self.version = version
    
    def format_ai_response(
        self,
        content: str,
        model_name: str = "gemini-pro",
        confidence: Optional[float] = None,
        is_truncated: bool = False,
        continuation_token: Optional[str] = None,
        processing_time: Optional[float] = None,
        request_id: Optional[str] = None
    ) -> StandardResponse:
        """格式化AI响应"""
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=content,
            metadata=ResponseMetadata(
                request_id=request_id or str(uuid.uuid4()),
                version=self.version,
                response_type=ResponseType.AI_CHAT,
                processing_time_ms=processing_time,
                source=model_name
            ),
            ai_model=model_name,
            confidence_score=confidence,
            is_truncated=is_truncated,
            continuation_token=continuation_token
        )
    
    def format_api_data(
        self,
        data: Any,
        api_source: str,
        processing_time: Optional[float] = None,
        cache_hit: bool = False,
        pagination: Optional[PaginationInfo] = None
    ) -> StandardResponse:
        """格式化API数据响应"""
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=data,
            metadata=ResponseMetadata(
                version=self.version,
                response_type=ResponseType.API_DATA,
                processing_time_ms=processing_time,
                source=api_source,
                cache_hit=cache_hit
            ),
            pagination=pagination
        )
    
    def format_error(
        self,
        error_code: ErrorCode,
        message: str,
        field: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        suggestion: Optional[str] = None,
        response_type: ResponseType = ResponseType.SYSTEM
    ) -> StandardResponse:
        """格式化错误响应"""
        error_detail = ErrorDetail(
            code=error_code,
            message=message,
            field=field,
            details=details,
            suggestion=suggestion
        )
        
        return StandardResponse(
            status=ResponseStatus.ERROR,
            errors=[error_detail],
            metadata=ResponseMetadata(
                version=self.version,
                response_type=response_type
            )
        )
    
    def format_validation_error(
        self,
        validation_errors: List[Dict[str, Any]]
    ) -> StandardResponse:
        """格式化验证错误"""
        errors = []
        for error in validation_errors:
            errors.append(ErrorDetail(
                code=ErrorCode.VALIDATION_ERROR,
                message=error.get("message", "Validation failed"),
                field=error.get("field"),
                details=error.get("details")
            ))
        
        return StandardResponse(
            status=ResponseStatus.ERROR,
            errors=errors,
            metadata=ResponseMetadata(
                version=self.version,
                response_type=ResponseType.VALIDATION
            )
        )
```

### 2.2 AI特定响应格式
**优化建议:**
```python
# 新建 app/core/ai_response_formatter.py
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

@dataclass
class AICapability:
    """AI能力描述"""
    name: str
    confidence: float
    available: bool
    description: Optional[str] = None

@dataclass
class AIProcessingInfo:
    """AI处理信息"""
    tokens_used: Optional[int] = None
    model_temperature: Optional[float] = None
    processing_steps: List[str] = None
    capabilities_used: List[AICapability] = None
    fallback_used: bool = False

class AIResponseFormatter(ResponseFormatter):
    """AI专用响应格式化器"""
    
    def format_travel_plan(
        self,
        itinerary: Dict[str, Any],
        recommendations: List[Dict[str, Any]],
        budget_breakdown: Dict[str, Any],
        processing_info: AIProcessingInfo,
        confidence_scores: Dict[str, float]
    ) -> StandardResponse:
        """格式化旅游规划响应"""
        
        travel_data = {
            "itinerary": itinerary,
            "recommendations": recommendations,
            "budget": budget_breakdown,
            "confidence_scores": confidence_scores,
            "generated_at": datetime.now().isoformat()
        }
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=travel_data,
            metadata=ResponseMetadata(
                version=self.version,
                response_type=ResponseType.TRAVEL_PLAN,
                processing_time_ms=processing_info.tokens_used * 0.1 if processing_info.tokens_used else None
            ),
            ai_model="gemini-travel-specialist",
            confidence_score=sum(confidence_scores.values()) / len(confidence_scores) if confidence_scores else None
        )
    
    def format_multimodal_response(
        self,
        text_response: str,
        image_analysis: Optional[Dict[str, Any]] = None,
        file_analysis: Optional[Dict[str, Any]] = None,
        processing_info: AIProcessingInfo = None
    ) -> StandardResponse:
        """格式化多模态响应"""
        
        multimodal_data = {
            "text": text_response,
            "analysis": {
                "images": image_analysis or {},
                "files": file_analysis or {}
            },
            "modalities_processed": []
        }
        
        if image_analysis:
            multimodal_data["modalities_processed"].append("image")
        if file_analysis:
            multimodal_data["modalities_processed"].append("file")
        
        return StandardResponse(
            status=ResponseStatus.SUCCESS,
            data=multimodal_data,
            metadata=ResponseMetadata(
                version=self.version,
                response_type=ResponseType.AI_CHAT,
                source="gemini-multimodal"
            ),
            ai_model="gemini-pro-vision"
        )
```

## 3. 版本控制机制

### 3.1 API版本管理
**优化建议:**
```python
# 新建 app/core/version_manager.py
from typing import Dict, Any, Callable, Optional
from enum import Enum

class APIVersion(str, Enum):
    V1_0 = "1.0"
    V1_1 = "1.1"
    V2_0 = "2.0"

class VersionManager:
    """API版本管理器"""
    
    def __init__(self):
        self.current_version = APIVersion.V1_0
        self.supported_versions = [APIVersion.V1_0, APIVersion.V1_1]
        self.deprecated_versions = []
        
        # 版本转换器
        self.version_converters = {
            (APIVersion.V1_0, APIVersion.V1_1): self._convert_v1_0_to_v1_1,
            (APIVersion.V1_1, APIVersion.V2_0): self._convert_v1_1_to_v2_0,
        }
        
        # 向后兼容转换器
        self.backward_converters = {
            (APIVersion.V1_1, APIVersion.V1_0): self._convert_v1_1_to_v1_0,
            (APIVersion.V2_0, APIVersion.V1_1): self._convert_v2_0_to_v1_1,
        }
    
    def is_version_supported(self, version: str) -> bool:
        """检查版本是否支持"""
        try:
            api_version = APIVersion(version)
            return api_version in self.supported_versions
        except ValueError:
            return False
    
    def convert_response(
        self,
        response: StandardResponse,
        target_version: str
    ) -> StandardResponse:
        """转换响应到目标版本"""
        if not self.is_version_supported(target_version):
            raise ValueError(f"Unsupported version: {target_version}")
        
        current_version = APIVersion(response.metadata.version)
        target_version_enum = APIVersion(target_version)
        
        if current_version == target_version_enum:
            return response
        
        # 查找转换路径
        converter_key = (current_version, target_version_enum)
        if converter_key in self.version_converters:
            return self.version_converters[converter_key](response)
        
        # 尝试向后兼容转换
        backward_key = (current_version, target_version_enum)
        if backward_key in self.backward_converters:
            return self.backward_converters[backward_key](response)
        
        # 如果没有直接转换器，尝试多步转换
        return self._multi_step_conversion(response, target_version_enum)
    
    def _convert_v1_0_to_v1_1(self, response: StandardResponse) -> StandardResponse:
        """V1.0 到 V1.1 转换"""
        # V1.1 添加了 confidence_score 字段
        if response.ai_model and not response.confidence_score:
            response.confidence_score = 0.8  # 默认置信度
        
        response.metadata.version = APIVersion.V1_1
        return response
    
    def _convert_v1_1_to_v1_0(self, response: StandardResponse) -> StandardResponse:
        """V1.1 到 V1.0 向后兼容转换"""
        # 移除 V1.1 特有字段
        response.confidence_score = None
        response.metadata.version = APIVersion.V1_0
        return response
    
    def get_version_info(self) -> Dict[str, Any]:
        """获取版本信息"""
        return {
            "current_version": self.current_version,
            "supported_versions": self.supported_versions,
            "deprecated_versions": self.deprecated_versions,
            "migration_guide": {
                "v1.0_to_v1.1": "Added confidence_score field for AI responses",
                "v1.1_to_v2.0": "Restructured error format and added metadata"
            }
        }
```

## 4. 向后兼容性策略

### 4.1 兼容性适配器
**优化建议:**
```python
# 新建 app/core/compatibility_adapter.py
from typing import Dict, Any, Optional

class CompatibilityAdapter:
    """向后兼容性适配器"""
    
    def __init__(self):
        self.legacy_field_mappings = {
            # 旧字段名 -> 新字段名
            "success": "status",
            "message": "data",
            "error": "errors[0].message",
            "response": "data"
        }
    
    def adapt_to_legacy_format(
        self,
        standard_response: StandardResponse,
        legacy_format: str = "v1_legacy"
    ) -> Dict[str, Any]:
        """适配到旧格式"""
        
        if legacy_format == "v1_legacy":
            return self._adapt_to_v1_legacy(standard_response)
        elif legacy_format == "simple_chat":
            return self._adapt_to_simple_chat(standard_response)
        else:
            return standard_response.dict()
    
    def _adapt_to_v1_legacy(self, response: StandardResponse) -> Dict[str, Any]:
        """适配到V1旧格式"""
        legacy_response = {
            "success": response.status == ResponseStatus.SUCCESS,
            "timestamp": response.metadata.timestamp.isoformat()
        }
        
        if response.data:
            if isinstance(response.data, str):
                legacy_response["response"] = response.data
            else:
                legacy_response["data"] = response.data
        
        if response.errors:
            legacy_response["error"] = response.errors[0].message
            legacy_response["error_code"] = response.errors[0].code
        
        if response.ai_model:
            legacy_response["model"] = response.ai_model
        
        return legacy_response
    
    def _adapt_to_simple_chat(self, response: StandardResponse) -> Dict[str, Any]:
        """适配到简单聊天格式"""
        return {
            "success": response.status == ResponseStatus.SUCCESS,
            "response": response.data if isinstance(response.data, str) else str(response.data),
            "error": response.errors[0].message if response.errors else None,
            "model": response.ai_model or "unknown"
        }
    
    def detect_client_version(self, headers: Dict[str, str]) -> Optional[str]:
        """检测客户端版本"""
        # 从请求头检测版本
        api_version = headers.get("X-API-Version")
        if api_version:
            return api_version
        
        # 从User-Agent检测
        user_agent = headers.get("User-Agent", "")
        if "SynTour-Mobile/1.0" in user_agent:
            return "v1_legacy"
        elif "SynTour-Web/2.0" in user_agent:
            return "2.0"
        
        # 默认返回最新版本
        return "1.0"
```

## 5. 实施策略

### 5.1 渐进式迁移计划
**实施步骤:**

1. **第一阶段 (1周)**: 实现标准响应格式
   - 创建 StandardResponse 模型
   - 实现 ResponseFormatter
   - 保持现有格式并行运行

2. **第二阶段 (1周)**: 添加版本控制
   - 实现 VersionManager
   - 添加版本检测中间件
   - 实现基础转换器

3. **第三阶段 (2周)**: 迁移现有端点
   - 逐个迁移API端点
   - 添加兼容性适配器
   - 更新前端客户端

4. **第四阶段 (1周)**: 完善和优化
   - 添加高级转换器
   - 优化性能
   - 完善文档

### 5.2 中间件实现
**优化建议:**
```python
# 新建 app/middleware/response_formatter_middleware.py
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
import json

class ResponseFormatterMiddleware(BaseHTTPMiddleware):
    def __init__(self, app, version_manager: VersionManager):
        super().__init__(app)
        self.version_manager = version_manager
        self.compatibility_adapter = CompatibilityAdapter()
    
    async def dispatch(self, request: Request, call_next):
        # 检测客户端版本
        client_version = self.compatibility_adapter.detect_client_version(
            dict(request.headers)
        )
        
        # 处理请求
        response = await call_next(request)
        
        # 如果是JSON响应，进行格式转换
        if response.headers.get("content-type") == "application/json":
            body = b""
            async for chunk in response.body_iterator:
                body += chunk
            
            try:
                response_data = json.loads(body.decode())
                
                # 如果已经是标准格式，进行版本转换
                if isinstance(response_data, dict) and "metadata" in response_data:
                    standard_response = StandardResponse(**response_data)
                    converted_response = self.version_manager.convert_response(
                        standard_response, client_version
                    )
                    response_data = converted_response.dict()
                
                # 如果需要兼容性适配
                elif client_version in ["v1_legacy", "simple_chat"]:
                    response_data = self.compatibility_adapter.adapt_to_legacy_format(
                        response_data, client_version
                    )
                
                new_body = json.dumps(response_data).encode()
                
                return Response(
                    content=new_body,
                    status_code=response.status_code,
                    headers=dict(response.headers),
                    media_type="application/json"
                )
            
            except Exception as e:
                # 如果转换失败，返回原响应
                return response
        
        return response
```

## 6. 监控和指标

### 关键指标
- 响应格式一致性率
- 版本转换成功率
- 向后兼容性覆盖率
- 客户端版本分布
- 格式转换性能

## 总结

这些AI输出格式标准化建议将显著提升SynTour系统的API一致性、可维护性和向后兼容性。建议按照渐进式迁移计划逐步实施，确保现有客户端的平滑过渡。
