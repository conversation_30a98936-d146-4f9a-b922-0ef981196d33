"""
Enhanced API Call Handler with Retry Logic and Circuit Breaker
Provides robust external API interaction with comprehensive error handling
"""

import asyncio
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import aiohttp
import logging
from dataclasses import dataclass

from .exception_handler import External<PERSON>IException, SynTourException, ErrorSeverity
from .logging_config import get_logger

logger = get_logger('api_handler')

class CircuitState(Enum):
    """Circuit breaker states"""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered

@dataclass
class RetryConfig:
    """Configuration for retry behavior"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True

@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker"""
    failure_threshold: int = 5
    recovery_timeout: int = 60
    success_threshold: int = 3

class CircuitBreaker:
    """
    Circuit breaker implementation for external API calls
    Prevents cascading failures by temporarily blocking calls to failing services
    """
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.next_attempt_time: Optional[datetime] = None
    
    async def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        
        if self.state == CircuitState.OPEN:
            if self._should_attempt_reset():
                self.state = CircuitState.HALF_OPEN
                self.success_count = 0
                logger.info(f"Circuit breaker {self.name} moved to HALF_OPEN state")
            else:
                raise ExternalAPIException(
                    service=self.name,
                    message="Circuit breaker is OPEN - service unavailable",
                    status_code=503
                )
        
        try:
            result = await func(*args, **kwargs)
            await self._on_success()
            return result
            
        except Exception as e:
            await self._on_failure()
            raise
    
    def _should_attempt_reset(self) -> bool:
        """Check if circuit breaker should attempt to reset"""
        if self.last_failure_time is None:
            return True
        
        return datetime.now() >= (
            self.last_failure_time + timedelta(seconds=self.config.recovery_timeout)
        )
    
    async def _on_success(self):
        """Handle successful call"""
        if self.state == CircuitState.HALF_OPEN:
            self.success_count += 1
            if self.success_count >= self.config.success_threshold:
                self.state = CircuitState.CLOSED
                self.failure_count = 0
                logger.info(f"Circuit breaker {self.name} moved to CLOSED state")
        else:
            self.failure_count = 0
    
    async def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()
        
        if self.state == CircuitState.HALF_OPEN:
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker {self.name} moved back to OPEN state")
        elif (self.state == CircuitState.CLOSED and 
              self.failure_count >= self.config.failure_threshold):
            self.state = CircuitState.OPEN
            logger.warning(f"Circuit breaker {self.name} moved to OPEN state after {self.failure_count} failures")

class APICallHandler:
    """
    Enhanced API call handler with retry logic, circuit breaker, and comprehensive monitoring
    Provides robust external API interaction capabilities
    """
    
    def __init__(self):
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.default_retry_config = RetryConfig()
        self.default_circuit_config = CircuitBreakerConfig()
        self.session: Optional[aiohttp.ClientSession] = None
        self.call_stats: Dict[str, Dict[str, Any]] = {}
    
    async def initialize(self):
        """Initialize HTTP session and resources"""
        if self.session is None:
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            connector = aiohttp.TCPConnector(
                limit=100,
                limit_per_host=20,
                ttl_dns_cache=300,
                use_dns_cache=True
            )
            
            self.session = aiohttp.ClientSession(
                timeout=timeout,
                connector=connector,
                headers={
                    'User-Agent': 'SynTour-Backend/2.1.0'
                }
            )
            logger.info("API call handler initialized")
    
    async def close(self):
        """Close HTTP session and cleanup resources"""
        if self.session:
            await self.session.close()
            self.session = None
            logger.info("API call handler closed")
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for service"""
        if service_name not in self.circuit_breakers:
            self.circuit_breakers[service_name] = CircuitBreaker(
                name=service_name,
                config=self.default_circuit_config
            )
        return self.circuit_breakers[service_name]
    
    async def safe_api_call(
        self,
        service_name: str,
        api_func: Callable,
        *args,
        retry_config: Optional[RetryConfig] = None,
        timeout: float = 30.0,
        **kwargs
    ) -> Any:
        """
        Execute API call with retry logic, circuit breaker, and comprehensive error handling
        
        Args:
            service_name: Name of the external service
            api_func: Async function to execute
            retry_config: Custom retry configuration
            timeout: Request timeout in seconds
            *args, **kwargs: Arguments for api_func
        
        Returns:
            Result from api_func
            
        Raises:
            ExternalAPIException: When all retry attempts fail
        """
        
        if not self.session:
            await self.initialize()
        
        config = retry_config or self.default_retry_config
        circuit_breaker = self.get_circuit_breaker(service_name)
        
        # Initialize stats for this service
        if service_name not in self.call_stats:
            self.call_stats[service_name] = {
                'total_calls': 0,
                'successful_calls': 0,
                'failed_calls': 0,
                'avg_response_time': 0.0,
                'last_call_time': None
            }
        
        stats = self.call_stats[service_name]
        stats['total_calls'] += 1
        stats['last_call_time'] = datetime.now().isoformat()
        
        start_time = time.time()
        last_exception = None
        
        for attempt in range(config.max_attempts):
            try:
                logger.debug(
                    f"API call attempt {attempt + 1}/{config.max_attempts} for {service_name}",
                    extra={'extra_context': {'service': service_name, 'attempt': attempt + 1}}
                )
                
                # Execute with circuit breaker protection
                result = await circuit_breaker.call(
                    self._execute_with_timeout,
                    api_func,
                    timeout,
                    *args,
                    **kwargs
                )
                
                # Update success stats
                duration = time.time() - start_time
                stats['successful_calls'] += 1
                stats['avg_response_time'] = (
                    (stats['avg_response_time'] * (stats['successful_calls'] - 1) + duration) /
                    stats['successful_calls']
                )
                
                logger.info(
                    f"API call to {service_name} succeeded on attempt {attempt + 1}",
                    extra={
                        'extra_context': {
                            'service': service_name,
                            'attempt': attempt + 1,
                            'duration': round(duration, 3)
                        }
                    }
                )
                
                return result
                
            except asyncio.TimeoutError as e:
                last_exception = ExternalAPIException(
                    service=service_name,
                    message=f"Request timeout after {timeout}s",
                    status_code=408
                )
                
            except aiohttp.ClientError as e:
                last_exception = ExternalAPIException(
                    service=service_name,
                    message=f"HTTP client error: {str(e)}",
                    status_code=getattr(e, 'status', 500)
                )
                
            except Exception as e:
                last_exception = ExternalAPIException(
                    service=service_name,
                    message=f"Unexpected error: {str(e)}",
                    original_exception=e
                )
            
            # Log retry attempt
            if attempt < config.max_attempts - 1:
                delay = self._calculate_retry_delay(attempt, config)
                
                logger.warning(
                    f"API call to {service_name} failed on attempt {attempt + 1}, retrying in {delay:.2f}s",
                    extra={
                        'extra_context': {
                            'service': service_name,
                            'attempt': attempt + 1,
                            'error': str(last_exception),
                            'retry_delay': delay
                        }
                    }
                )
                
                await asyncio.sleep(delay)
        
        # All attempts failed
        stats['failed_calls'] += 1
        
        logger.error(
            f"API call to {service_name} failed after {config.max_attempts} attempts",
            extra={
                'extra_context': {
                    'service': service_name,
                    'total_attempts': config.max_attempts,
                    'final_error': str(last_exception)
                }
            }
        )
        
        raise last_exception
    
    async def _execute_with_timeout(
        self,
        api_func: Callable,
        timeout: float,
        *args,
        **kwargs
    ) -> Any:
        """Execute function with timeout"""
        try:
            return await asyncio.wait_for(
                api_func(*args, **kwargs),
                timeout=timeout
            )
        except asyncio.TimeoutError:
            raise asyncio.TimeoutError(f"Operation timed out after {timeout}s")
    
    def _calculate_retry_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calculate delay for retry attempt with exponential backoff and jitter"""
        
        # Exponential backoff
        delay = config.base_delay * (config.exponential_base ** attempt)
        
        # Apply maximum delay limit
        delay = min(delay, config.max_delay)
        
        # Add jitter to prevent thundering herd
        if config.jitter:
            import random
            jitter_range = delay * 0.1  # 10% jitter
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    async def http_get(
        self,
        service_name: str,
        url: str,
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """HTTP GET request with error handling"""
        
        async def _get():
            async with self.session.get(
                url,
                headers=headers,
                params=params,
                **kwargs
            ) as response:
                response.raise_for_status()
                return await response.json()
        
        return await self.safe_api_call(service_name, _get)
    
    async def http_post(
        self,
        service_name: str,
        url: str,
        data: Optional[Dict[str, Any]] = None,
        json_data: Optional[Dict[str, Any]] = None,
        headers: Optional[Dict[str, str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """HTTP POST request with error handling"""
        
        async def _post():
            async with self.session.post(
                url,
                data=data,
                json=json_data,
                headers=headers,
                **kwargs
            ) as response:
                response.raise_for_status()
                return await response.json()
        
        return await self.safe_api_call(service_name, _post)
    
    def get_service_stats(self, service_name: Optional[str] = None) -> Dict[str, Any]:
        """Get API call statistics for service(s)"""
        if service_name:
            return self.call_stats.get(service_name, {})
        return self.call_stats.copy()
    
    def get_circuit_breaker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get status of all circuit breakers"""
        status = {}
        for name, breaker in self.circuit_breakers.items():
            status[name] = {
                'state': breaker.state.value,
                'failure_count': breaker.failure_count,
                'success_count': breaker.success_count,
                'last_failure_time': breaker.last_failure_time.isoformat() if breaker.last_failure_time else None
            }
        return status
    
    def reset_circuit_breaker(self, service_name: str):
        """Manually reset circuit breaker for a service"""
        if service_name in self.circuit_breakers:
            breaker = self.circuit_breakers[service_name]
            breaker.state = CircuitState.CLOSED
            breaker.failure_count = 0
            breaker.success_count = 0
            breaker.last_failure_time = None
            logger.info(f"Circuit breaker for {service_name} manually reset")

# Global API call handler instance
api_call_handler = APICallHandler()
