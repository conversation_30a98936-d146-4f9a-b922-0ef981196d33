# app/core/api_client.py
import asyncio
import aiohttp
import logging
from typing import Dict, Any, Optional, Union
from dataclasses import dataclass
import json
import time
from urllib.parse import urlencode

from .api_config import APIConfig, get_api_config

logger = logging.getLogger(__name__)

@dataclass
class APIResponse:
    """Standardized API response"""
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    status_code: Optional[int] = None
    headers: Optional[Dict[str, str]] = None
    response_time: Optional[float] = None

class APIError(Exception):
    """Custom API error with detailed information"""
    def __init__(self, message: str, status_code: Optional[int] = None, 
                 response_data: Optional[Dict] = None):
        self.message = message
        self.status_code = status_code
        self.response_data = response_data
        super().__init__(self.message)

class SecureAPIClient:
    """Secure API client with retry, logging, and error handling"""
    
    def __init__(self, api_name: str, user_agent: str = "SynTour/1.0"):
        self.api_name = api_name
        self.config = get_api_config(api_name)
        self.user_agent = user_agent
        self.session = None
    
    async def __aenter__(self):
        """Async context manager entry"""
        connector = aiohttp.TCPConnector(limit=100, limit_per_host=30)
        timeout = aiohttp.ClientTimeout(total=self.config.get('timeout', APIConfig.DEFAULT_TIMEOUT))
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={'User-Agent': self.user_agent}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    def _mask_sensitive_data(self, data: Optional[Dict[str, Any]]) -> Dict[str, Any]:
        """Mask sensitive information in logs"""
        if data is None:
            return {}
            
        if not APIConfig.LOG_SENSITIVE_DATA:
            sensitive_keys = ['key', 'apikey', 'api_key', 'token', 'password', 'secret']
            masked_data = data.copy()
            
            for key, value in masked_data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    if isinstance(value, str) and len(value) > 8:
                        masked_data[key] = f"{value[:4]}...{value[-4:]}"
                    else:
                        masked_data[key] = "***"
            
            return masked_data
        return data
    
    async def _make_request_with_retry(
        self,
        method: str,
        url: str,
        **kwargs
    ) -> APIResponse:
        """Make HTTP request with retry logic"""
        start_time = time.time()
        last_exception = None
        
        for attempt in range(APIConfig.MAX_RETRIES + 1):
            try:
                # Log request (with masked sensitive data)
                log_params = self._mask_sensitive_data(kwargs.get('params', {}))
                logger.info(f"{self.api_name} API request: {method} {url}")
                logger.debug(f"Request params: {log_params}")
                
                async with self.session.request(method, url, **kwargs) as response:
                    response_time = time.time() - start_time
                    response_text = await response.text()
                    
                    # Handle successful responses
                    if 200 <= response.status < 300:
                        try:
                            data = await response.json() if response_text else {}
                            logger.info(f"{self.api_name} API success: {response.status} ({response_time:.2f}s)")
                            
                            return APIResponse(
                                success=True,
                                data=data,
                                status_code=response.status,
                                headers=dict(response.headers),
                                response_time=response_time
                            )
                        except json.JSONDecodeError:
                            # Handle non-JSON responses (like images)
                            return APIResponse(
                                success=True,
                                data={"content": response_text},
                                status_code=response.status,
                                headers=dict(response.headers),
                                response_time=response_time
                            )
                    
                    # Handle client errors (4xx) - don't retry
                    elif 400 <= response.status < 500:
                        error_msg = self._parse_error_message(response_text, response.status)
                        logger.error(f"{self.api_name} API client error: {response.status} - {error_msg}")
                        
                        return APIResponse(
                            success=False,
                            error=error_msg,
                            status_code=response.status,
                            headers=dict(response.headers),
                            response_time=response_time
                        )
                    
                    # Handle server errors (5xx) - retry
                    else:
                        error_msg = f"Server error: {response.status}"
                        logger.warning(f"{self.api_name} API server error: {response.status} (attempt {attempt + 1})")
                        
                        if attempt == APIConfig.MAX_RETRIES:
                            return APIResponse(
                                success=False,
                                error=error_msg,
                                status_code=response.status,
                                headers=dict(response.headers),
                                response_time=response_time
                            )
                        
                        # Wait before retry with exponential backoff
                        wait_time = APIConfig.RETRY_BACKOFF_FACTOR ** attempt
                        await asyncio.sleep(wait_time)
                        continue
            
            except asyncio.TimeoutError as e:
                last_exception = e
                logger.warning(f"{self.api_name} API timeout (attempt {attempt + 1})")
                
                if attempt == APIConfig.MAX_RETRIES:
                    return APIResponse(
                        success=False,
                        error="Request timeout",
                        response_time=time.time() - start_time
                    )
                
                wait_time = APIConfig.RETRY_BACKOFF_FACTOR ** attempt
                await asyncio.sleep(wait_time)
            
            except aiohttp.ClientError as e:
                last_exception = e
                logger.warning(f"{self.api_name} API connection error (attempt {attempt + 1}): {str(e)}")
                
                if attempt == APIConfig.MAX_RETRIES:
                    return APIResponse(
                        success=False,
                        error=f"Connection error: {str(e)}",
                        response_time=time.time() - start_time
                    )
                
                wait_time = APIConfig.RETRY_BACKOFF_FACTOR ** attempt
                await asyncio.sleep(wait_time)
        
        # Should not reach here, but just in case
        return APIResponse(
            success=False,
            error=f"Max retries exceeded: {str(last_exception)}",
            response_time=time.time() - start_time
        )
    
    def _parse_error_message(self, response_text: str, status_code: int) -> str:
        """Parse error message from API response"""
        try:
            error_data = json.loads(response_text)
            
            # Common error message fields
            error_fields = ['message', 'error', 'detail', 'error_description', 'msg']
            
            for field in error_fields:
                if field in error_data:
                    return str(error_data[field])
            
            # If no standard error field, return the whole response (truncated)
            return str(error_data)[:200] + "..." if len(str(error_data)) > 200 else str(error_data)
            
        except json.JSONDecodeError:
            # If not JSON, return truncated text
            return response_text[:200] + "..." if len(response_text) > 200 else response_text
    
    async def get(self, url: str, params: Optional[Dict] = None, **kwargs) -> APIResponse:
        """Make GET request"""
        return await self._make_request_with_retry('GET', url, params=params, **kwargs)
    
    async def post(self, url: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None, **kwargs) -> APIResponse:
        """Make POST request"""
        return await self._make_request_with_retry('POST', url, data=data, json=json_data, **kwargs)
    
    async def put(self, url: str, data: Optional[Dict] = None, json_data: Optional[Dict] = None, **kwargs) -> APIResponse:
        """Make PUT request"""
        return await self._make_request_with_retry('PUT', url, data=data, json=json_data, **kwargs)
    
    async def delete(self, url: str, **kwargs) -> APIResponse:
        """Make DELETE request"""
        return await self._make_request_with_retry('DELETE', url, **kwargs)