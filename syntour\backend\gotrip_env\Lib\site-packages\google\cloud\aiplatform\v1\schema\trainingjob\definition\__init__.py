# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
from google.cloud.aiplatform.v1.schema.trainingjob.definition import (
    gapic_version as package_version,
)

__version__ = package_version.__version__


from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_classification import (
    AutoMlImageClassification,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_classification import (
    AutoMlImageClassificationInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_classification import (
    AutoMlImageClassificationMetadata,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_object_detection import (
    AutoMlImageObjectDetection,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_object_detection import (
    AutoMlImageObjectDetectionInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_object_detection import (
    AutoMlImageObjectDetectionMetadata,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_segmentation import (
    AutoMlImageSegmentation,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_segmentation import (
    AutoMlImageSegmentationInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_image_segmentation import (
    AutoMlImageSegmentationMetadata,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_tables import (
    AutoMlTables,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_tables import (
    AutoMlTablesInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_tables import (
    AutoMlTablesMetadata,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_classification import (
    AutoMlTextClassification,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_classification import (
    AutoMlTextClassificationInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_extraction import (
    AutoMlTextExtraction,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_extraction import (
    AutoMlTextExtractionInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_sentiment import (
    AutoMlTextSentiment,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_text_sentiment import (
    AutoMlTextSentimentInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_action_recognition import (
    AutoMlVideoActionRecognition,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_action_recognition import (
    AutoMlVideoActionRecognitionInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_classification import (
    AutoMlVideoClassification,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_classification import (
    AutoMlVideoClassificationInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_object_tracking import (
    AutoMlVideoObjectTracking,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.automl_video_object_tracking import (
    AutoMlVideoObjectTrackingInputs,
)
from google.cloud.aiplatform.v1.schema.trainingjob.definition_v1.types.export_evaluated_data_items_config import (
    ExportEvaluatedDataItemsConfig,
)

__all__ = (
    "AutoMlImageClassification",
    "AutoMlImageClassificationInputs",
    "AutoMlImageClassificationMetadata",
    "AutoMlImageObjectDetection",
    "AutoMlImageObjectDetectionInputs",
    "AutoMlImageObjectDetectionMetadata",
    "AutoMlImageSegmentation",
    "AutoMlImageSegmentationInputs",
    "AutoMlImageSegmentationMetadata",
    "AutoMlTables",
    "AutoMlTablesInputs",
    "AutoMlTablesMetadata",
    "AutoMlTextClassification",
    "AutoMlTextClassificationInputs",
    "AutoMlTextExtraction",
    "AutoMlTextExtractionInputs",
    "AutoMlTextSentiment",
    "AutoMlTextSentimentInputs",
    "AutoMlVideoActionRecognition",
    "AutoMlVideoActionRecognitionInputs",
    "AutoMlVideoClassification",
    "AutoMlVideoClassificationInputs",
    "AutoMlVideoObjectTracking",
    "AutoMlVideoObjectTrackingInputs",
    "ExportEvaluatedDataItemsConfig",
)
