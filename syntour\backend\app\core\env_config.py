# app/core/env_config.py
import os
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

class EnvironmentConfig:
    """Centralized environment configuration with defaults"""
    
    # Development defaults for common settings
    DEVELOPMENT_DEFAULTS = {
        # Timeout settings
        'API_TIMEOUT_SECONDS': '30',
        'FLIGHT_API_TIMEOUT_SECONDS': '60',
        'GOOGLE_PLACES_TIMEOUT_SECONDS': '30',
        
        # Rate limiting defaults
        'DEFAULT_RATE_LIMIT': '100',
        'FLIGHT_API_RATE_LIMIT': '50',
        'GOOGLE_PLACES_RATE_LIMIT': '100',
        
        # Cache settings
        'CACHE_MAX_SIZE': '2000',
        'DEFAULT_CACHE_TTL': '300',
        
        # Google Places field configuration
        'GOOGLE_PLACES_DEFAULT_FIELDS': 'place_id,name,formatted_address,geometry,types,rating,user_ratings_total,business_status',
        'GOOG<PERSON>_PLACES_SUMMARY_FIELDS': 'place_id,name,formatted_address,geometry,types,rating',
        
        # Logging configuration
        'LOG_LEVEL': 'INFO',
        'LOG_FORMAT': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        
        # File upload settings
        'MAX_FILE_SIZE_MB': '10',
        'GCS_UPLOAD_THRESHOLD_MB': '20',
        
        # Development server settings
        'HOST': '0.0.0.0',
        'PORT': '8000',
        'RELOAD': 'true',
        
        # CORS settings for development
        'CORS_ORIGINS': 'http://localhost:3000,http://localhost:8000,http://localhost:5173',
        
        # Default locations for testing
        'DEFAULT_LATITUDE': '40.7128',
        'DEFAULT_LONGITUDE': '-74.0060',
        'DEFAULT_LOCATION': 'New York, NY',
        
        # API endpoint defaults (for testing/development)
        'ONEWAY_TRIP_API_ENDPOINT': 'https://api.flightapi.io/onewaytrip',
        'ROUND_TRIP_API_ENDPOINT': 'https://api.flightapi.io/roundtrip',
        'AIRPORT_SCHEDULE_API_ENDPOINT': 'https://api.flightapi.io/schedule',
    }
    
    @classmethod
    def get_env_var(cls, key: str, default: Optional[str] = None, required: bool = False) -> Optional[str]:
        """
        Get environment variable with optional default and validation
        
        Args:
            key: Environment variable name
            default: Default value if not found
            required: Whether the variable is required (raises error if missing)
            
        Returns:
            Environment variable value or default
            
        Raises:
            ValueError: If required variable is missing
        """
        value = os.getenv(key)
        
        if value is not None:
            return value
        
        # Try development default
        if default is None and key in cls.DEVELOPMENT_DEFAULTS:
            default = cls.DEVELOPMENT_DEFAULTS[key]
            logger.debug(f"Using development default for {key}: {default}")
        
        if value is None and default is not None:
            logger.info(f"Using default value for {key}")
            return default
        
        if required and value is None:
            raise ValueError(f"Required environment variable {key} is not set")
        
        return value
    
    @classmethod
    def get_int_env_var(cls, key: str, default: Optional[int] = None, required: bool = False) -> Optional[int]:
        """Get environment variable as integer"""
        str_value = cls.get_env_var(key, str(default) if default is not None else None, required)
        if str_value is None:
            return None
        
        try:
            return int(str_value)
        except ValueError:
            logger.warning(f"Invalid integer value for {key}: {str_value}, using default: {default}")
            return default
    
    @classmethod
    def get_float_env_var(cls, key: str, default: Optional[float] = None, required: bool = False) -> Optional[float]:
        """Get environment variable as float"""
        str_value = cls.get_env_var(key, str(default) if default is not None else None, required)
        if str_value is None:
            return None
        
        try:
            return float(str_value)
        except ValueError:
            logger.warning(f"Invalid float value for {key}: {str_value}, using default: {default}")
            return default
    
    @classmethod
    def get_bool_env_var(cls, key: str, default: Optional[bool] = None, required: bool = False) -> Optional[bool]:
        """Get environment variable as boolean"""
        str_value = cls.get_env_var(key, str(default).lower() if default is not None else None, required)
        if str_value is None:
            return None
        
        return str_value.lower() in ('true', '1', 'yes', 'on')
    
    @classmethod
    def get_list_env_var(cls, key: str, default: Optional[list] = None, separator: str = ',', required: bool = False) -> Optional[list]:
        """Get environment variable as list"""
        str_value = cls.get_env_var(key, separator.join(default) if default else None, required)
        if str_value is None:
            return None
        
        return [item.strip() for item in str_value.split(separator) if item.strip()]
    
    @classmethod
    def validate_required_vars(cls, required_vars: list) -> Dict[str, bool]:
        """
        Validate that all required environment variables are set
        
        Args:
            required_vars: List of required environment variable names
            
        Returns:
            Dictionary mapping variable names to whether they're set
        """
        results = {}
        for var in required_vars:
            value = os.getenv(var)
            results[var] = value is not None and value.strip() != ''
            
            if not results[var]:
                logger.warning(f"Required environment variable {var} is not set")
        
        return results
    
    @classmethod
    def get_api_credentials(cls, api_name: str) -> Dict[str, Optional[str]]:
        """
        Get API credentials with fallback handling
        
        Args:
            api_name: Name of the API (e.g., 'amadeus', 'google_places')
            
        Returns:
            Dictionary of credential keys and values
        """
        credentials = {}
        
        if api_name.lower() == 'amadeus':
            # Handle both typo and correct versions
            credentials['api_key'] = (
                cls.get_env_var('AMADEUS_API_KEY') or 
                cls.get_env_var('AMADUES_API_KEY')  # Handle typo
            )
            credentials['api_secret'] = cls.get_env_var('AMADEUS_API_SECRET')
            
        elif api_name.lower() == 'google_places':
            credentials['api_key'] = cls.get_env_var('GOOGLE_PLACES_API_KEY')
            
        elif api_name.lower() == 'flight_api':
            credentials['api_key'] = cls.get_env_var('FLIGHT_API_KEY')
            
        elif api_name.lower() == 'hotelbeds':
            credentials['api_key'] = cls.get_env_var('HOTELBEDS_API_KEY')
            credentials['api_secret'] = cls.get_env_var('HOTELBEDS_SECRET')
            
        elif api_name.lower() == 'openweather':
            credentials['api_key'] = cls.get_env_var('OPEN_WEATHER_MAP_API_KEY')
            
        elif api_name.lower() == 'tomorrow_io':
            credentials['api_key'] = cls.get_env_var('TOMORROW_IO_API_KEY')
            
        elif api_name.lower() == 'geoapify':
            credentials['api_key'] = cls.get_env_var('GEOAPIFY_API_KEY')
        
        return credentials
    
    @classmethod
    def get_development_info(cls) -> Dict[str, Any]:
        """Get information about development environment setup"""
        return {
            'available_defaults': list(cls.DEVELOPMENT_DEFAULTS.keys()),
            'environment': cls.get_env_var('ENVIRONMENT', 'development'),
            'debug_mode': cls.get_bool_env_var('DEBUG', True),
            'defaults_used': [
                key for key in cls.DEVELOPMENT_DEFAULTS.keys()
                if os.getenv(key) is None
            ]
        }

# Convenience functions for common use cases
def get_timeout_config() -> Dict[str, int]:
    """Get timeout configuration with defaults"""
    return {
        'default': EnvironmentConfig.get_int_env_var('API_TIMEOUT_SECONDS', 30),
        'flight_api': EnvironmentConfig.get_int_env_var('FLIGHT_API_TIMEOUT_SECONDS', 60),
        'google_places': EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_TIMEOUT_SECONDS', 30),
    }

def get_rate_limit_config() -> Dict[str, int]:
    """Get rate limiting configuration with defaults"""
    return {
        'default': EnvironmentConfig.get_int_env_var('DEFAULT_RATE_LIMIT', 100),
        'flight_api': EnvironmentConfig.get_int_env_var('FLIGHT_API_RATE_LIMIT', 50),
        'google_places': EnvironmentConfig.get_int_env_var('GOOGLE_PLACES_RATE_LIMIT', 100),
    }

def get_cache_config() -> Dict[str, int]:
    """Get cache configuration with defaults"""
    return {
        'max_size': EnvironmentConfig.get_int_env_var('CACHE_MAX_SIZE', 2000),
        'default_ttl': EnvironmentConfig.get_int_env_var('DEFAULT_CACHE_TTL', 300),
    }