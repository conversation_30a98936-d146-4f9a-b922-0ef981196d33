# app/services/geoapify_service.py
import os
import aiohttp
import asyncio
from typing import Optional, List, Dict, Any
from fastapi import HTTPException
import logging

from app.models.geoapify_model import (
    LocationSearchResponse,
    LocationDetailsResponse,
    LocationFeature,
    LocationDetailsFeature,
    PlaceType,
    GeoapifyError,
    extract_coordinates,
    format_address,
    is_business_location
)

# Configure logging
logger = logging.getLogger(__name__)

# Geoapify API configuration - Use environment variable for URL selection
GEOAPIFY_BASE_URL = os.getenv('GEOAPIFY_BASE_URL', 'https://api.geoapify.com/v2')
GEOAPIFY_TIMEOUT = int(os.getenv('GEOAPIFY_TIMEOUT_SECONDS', '30'))

def get_geoapify_credentials() -> str:
    """Get Geoapify API key from environment variables"""
    api_key = os.getenv("GEOAPIFY_API_KEY")
    if not api_key:
        raise ValueError("GEOAPIFY_API_KEY environment variable must be set")
    return api_key

async def geoapify_request(
    endpoint: str,
    params: Optional[Dict[str, Any]] = None,
    method: str = "GET"
) -> Dict[str, Any]:
    """Make a request to Geoapify API"""
    try:
        api_key = get_geoapify_credentials()
        
        # Prepare URL and parameters
        url = f"{GEOAPIFY_BASE_URL}{endpoint}"
        request_params = params or {}
        request_params["apiKey"] = api_key
        
        logger.info(f"Geoapify API request: {method} {url}")
        logger.debug(f"Request parameters: {request_params}")
        
        async with aiohttp.ClientSession(timeout=aiohttp.ClientTimeout(total=GEOAPIFY_TIMEOUT)) as session:
            if method.upper() == "GET":
                async with session.get(url, params=request_params) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        data = await response.json()
                        logger.info(f"Geoapify API success: {response.status}")
                        return data
                    else:
                        logger.error(f"Geoapify API error: {response.status} - {response_text}")
                        if response.status == 401:
                            raise GeoapifyError("Invalid API key", response.status)
                        elif response.status == 403:
                            raise GeoapifyError("API access forbidden", response.status)
                        elif response.status == 429:
                            raise GeoapifyError("API rate limit exceeded", response.status)
                        else:
                            raise GeoapifyError(f"API error: {response.status}", response.status)
            else:
                raise GeoapifyError(f"Unsupported HTTP method: {method}")
                
    except aiohttp.ClientError as e:
        logger.error(f"Geoapify API connection error: {str(e)}")
        raise GeoapifyError(f"Connection error: {str(e)}")
    except GeoapifyError:
        raise
    except Exception as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(status_code=500, detail="Internal server error")

async def search_locations(
    text: str,
    limit: int = 10,
    categories: Optional[List[str]] = None,
    bias_location: Optional[tuple[float, float]] = None,
    country_codes: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Search for locations using text query
    
    Args:
        text: Search query text
        limit: Maximum number of results (1-100)
        categories: List of place categories to filter by
        bias_location: (lat, lon) tuple to bias results towards
        country_codes: List of ISO country codes to limit search
    
    Returns:
        Dictionary containing search results
    """
    # Validate input
    if not text or not text.strip():
        raise ValueError("Search text cannot be empty")
    
    params = {
        "text": text.strip(),
        "limit": min(max(limit, 1), 100)  # Ensure limit is between 1-100
    }
    
    # Geoapify requires either 'type' or 'categories' parameter
    if categories:
        params["categories"] = ",".join(categories)
    else:
        # Default to general place types if no categories specified
        params["categories"] = "accommodation,commercial,catering,entertainment"
    
    # For text search, Geoapify API requires either bias or filter
    # Bias (proximity) works better than global filters for text search
    if bias_location:
        lat, lon = bias_location
        params["bias"] = f"proximity:{lon},{lat}"
    else:
        # If no bias provided, try to extract location from text or use default
        # For better results, we'll use a default major city bias
        # This is better than global rect filter which returns 0 results
        default_coords = (40.7128, -74.0060)  # NYC as default
        params["bias"] = f"proximity:{default_coords[1]},{default_coords[0]}"
    
    # Handle country codes with proper filter syntax
    if country_codes:
        # Use place filter for country codes (works better than countrycode)
        country_filter = f"place:{','.join(country_codes)}"
        if "filter" in params:
            params["filter"] += f"|{country_filter}"
        else:
            params["filter"] = country_filter
    
    return await geoapify_request("/places", params)

async def search_locations_by_coordinates(
    lat: float,
    lon: float,
    radius: int = 1000,
    limit: int = 10,
    categories: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Search for locations near specific coordinates
    
    Args:
        lat: Latitude
        lon: Longitude
        radius: Search radius in meters (max 50000)
        limit: Maximum number of results
        categories: List of place categories to filter by
    
    Returns:
        Dictionary containing search results
    """
    params = {
        "filter": f"circle:{lon},{lat},{min(radius, 50000)}",
        "limit": min(max(limit, 1), 100)
    }
    
    # Geoapify requires either 'type' or 'categories' parameter
    if categories:
        params["categories"] = ",".join(categories)
    else:
        # Default to general place types if no categories specified
        params["categories"] = "accommodation,commercial,catering,entertainment"
    
    return await geoapify_request("/places", params)

async def get_location_details(place_id: str) -> Dict[str, Any]:
    """
    Get detailed information about a specific place
    
    Args:
        place_id: Unique place identifier
    
    Returns:
        Dictionary containing detailed place information
    """
    params = {
        "id": place_id
    }
    
    return await geoapify_request("/place-details", params)

async def autocomplete_locations(
    text: str,
    limit: int = 5,
    bias_location: Optional[tuple[float, float]] = None,
    country_codes: Optional[List[str]] = None
) -> Dict[str, Any]:
    """
    Get autocomplete suggestions for location search
    
    Args:
        text: Partial search text
        limit: Maximum number of suggestions
        bias_location: (lat, lon) tuple to bias results towards
        country_codes: List of ISO country codes to limit search
    
    Returns:
        Dictionary containing autocomplete suggestions
    """
    # Validate input
    if not text or not text.strip():
        raise ValueError("Autocomplete text cannot be empty")
    
    params = {
        "text": text.strip(),
        "limit": min(max(limit, 1), 20)
    }
    
    # For autocomplete, include places and populated places for better results
    params["categories"] = "accommodation,commercial,catering,entertainment,populated_place"
    
    # Autocomplete REQUIRES bias or filter - bias works better for partial text
    if bias_location:
        lat, lon = bias_location
        params["bias"] = f"proximity:{lon},{lat}"
    else:
        # Use default bias for better autocomplete results
        # Global rect filter doesn't work well for autocomplete
        default_coords = (40.7128, -74.0060)  # NYC as default
        params["bias"] = f"proximity:{default_coords[1]},{default_coords[0]}"
    
    # Handle country codes with proper filter syntax
    if country_codes:
        country_filter = f"place:{','.join(country_codes)}"
        if "filter" in params:
            params["filter"] += f"|{country_filter}"
        else:
            params["filter"] = country_filter
    
    return await geoapify_request("/places", params)

# Utility functions
def filter_useful_properties(raw_properties: Dict[str, Any]) -> Dict[str, Any]:
    """Filter out useful properties from raw API response"""
    useful_fields = {
        "name", "formatted", "address_line1", "address_line2", 
        "city", "state", "country", "country_code", "postcode",
        "place_id", "categories", "phone", "website", "email",
        "opening_hours", "rating", "description", "brand", "operator"
    }
    
    return {k: v for k, v in raw_properties.items() if k in useful_fields and v is not None}

def extract_location_summary(feature: LocationFeature) -> Dict[str, Any]:
    """Extract a summary of key location information"""
    lat, lon = extract_coordinates(feature)
    props = feature.properties
    
    return {
        "name": props.name,
        "address": format_address(props),
        "coordinates": {"latitude": lat, "longitude": lon},
        "city": props.city,
        "country": props.country,
        "categories": props.categories,
        "is_business": is_business_location(props),
        "phone": props.phone,
        "website": props.website
    }

def validate_coordinates(lat: float, lon: float) -> bool:
    """Validate latitude and longitude values"""
    return -90 <= lat <= 90 and -180 <= lon <= 180

def validate_place_categories(categories: List[str]) -> List[str]:
    """Validate and filter place categories"""
    valid_categories = [cat.value for cat in PlaceType]
    return [cat for cat in categories if cat in valid_categories]