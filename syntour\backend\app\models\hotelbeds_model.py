from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime

# Content wrapper for text fields (used throughout the API)
class Content(BaseModel):
    content: str

# Address model with detailed structure
class Address(BaseModel):
    content: Optional[str] = None
    street: Optional[str] = None
    number: Optional[str] = None

# Coordinate model for hotel location
class Coordinates(BaseModel):
    longitude: float
    latitude: float

# Phone model
class Phone(BaseModel):
    phoneNumber: str
    phoneType: str

# Email model
class Email(BaseModel):
    email: str

# Web model for hotel websites
class Web(BaseModel):
    web: str

# Category model
class Category(BaseModel):
    code: str
    description: Optional[Content] = None

# Category Group model
class CategoryGroup(BaseModel):
    code: str
    description: Optional[Content] = None

# Accommodation type model
class AccommodationType(BaseModel):
    code: str
    typeMultiDescription: Optional[Content] = None
    typeDescription: Optional[str] = None

# Chain model
class Chain(BaseModel):
    code: str
    description: Optional[Content] = None

# Segment model
class Segment(BaseModel):
    code: int
    description: Optional[Content] = None

# Board model for meal plans
class Board(BaseModel):
    code: str
    description: Optional[Content] = None

# Country model
class Country(BaseModel):
    code: str
    isoCode: str
    description: Optional[Content] = None

# State model
class State(BaseModel):
    code: str
    name: Optional[str] = None

# Destination model
class Destination(BaseModel):
    code: str
    name: Optional[Content] = None
    countryCode: Optional[str] = None

# Zone model
class Zone(BaseModel):
    zoneCode: int
    name: Optional[str] = None
    description: Optional[Content] = None

# Facility model
class Facility(BaseModel):
    facilityCode: int
    facilityGroupCode: int
    order: Optional[int] = None
    indLogic: Optional[bool] = None
    indFee: Optional[bool] = None
    indYesOrNo: Optional[bool] = None
    number: Optional[int] = None
    voucher: Optional[bool] = None
    distance: Optional[int] = None
    timeFrom: Optional[str] = None  # Additional field for time-based facilities
    timeTo: Optional[str] = None    # Additional field for time-based facilities
    dateTo: Optional[str] = None    # Additional field for date-based facilities

# Terminal model for airport/transport info
class Terminal(BaseModel):
    terminalCode: str
    distance: Optional[int] = None

# Interest point model
class InterestPoint(BaseModel):
    facilityCode: int
    facilityGroupCode: int
    order: Optional[int] = None
    poiName: Optional[str] = None
    distance: Optional[str] = None

# Issue model for hotel problems/notices
class Issue(BaseModel):
    issueCode: str
    issueType: Optional[str] = None
    dateFrom: Optional[str] = None
    dateTo: Optional[str] = None
    order: Optional[int] = None
    alternative: Optional[bool] = None

# Wildcard model for additional info
class Wildcard(BaseModel):
    roomType: Optional[str] = None
    roomCode: Optional[str] = None
    characteristicCode: Optional[str] = None
    hotelRoomDescription: Optional[Dict[str, Any]] = None

# Image model
class Image(BaseModel):
    imageTypeCode: str
    path: str
    order: Optional[int] = None
    visualOrder: Optional[int] = None
    roomCode: Optional[str] = None
    roomType: Optional[str] = None
    characteristicCode: Optional[str] = None

# Room model
class Room(BaseModel):
    roomCode: str
    isParentRoom: Optional[bool] = None
    minPax: Optional[int] = None
    maxPax: Optional[int] = None
    maxAdults: Optional[int] = None
    maxChildren: Optional[int] = None
    minAdults: Optional[int] = None
    roomType: Optional[str] = None
    characteristicCode: Optional[str] = None
    roomFacilities: Optional[List[Facility]] = None
    roomStays: Optional[List[Dict[str, Any]]] = None

# Basic Hotel model (for hotel list responses)
class Hotel(BaseModel):
    code: int
    name: Optional[Content] = None  # API sometimes doesn't include name in basic response
    description: Optional[Content] = None
    countryCode: Optional[str] = None  # Not always present in basic response
    stateCode: Optional[str] = None
    destinationCode: Optional[str] = None
    zoneCode: Optional[int] = None
    coordinates: Optional[Coordinates] = None
    categoryCode: Optional[str] = None
    categoryGroupCode: Optional[str] = None
    chainCode: Optional[str] = None
    accommodationTypeCode: Optional[str] = None
    boardCodes: Optional[List[str]] = None
    segmentCodes: Optional[List[int]] = None
    address: Optional[Address] = None
    postalCode: Optional[str] = None
    city: Optional[Content] = None
    email: Optional[str] = None
    license: Optional[str] = None
    phones: Optional[List[Phone]] = None
    web: Optional[str] = None
    lastUpdate: Optional[str] = None  # API returns string, not datetime
    S2C: Optional[str] = None  # Supplier to client remarks
    ranking: Optional[int] = None

# Detailed Hotel model (for hotel details responses)
class HotelDetails(BaseModel):
    code: int
    name: Optional[Content] = None
    description: Optional[Content] = None
    country: Optional[Country] = None
    state: Optional[State] = None
    destination: Optional[Destination] = None
    zone: Optional[Zone] = None
    coordinates: Optional[Coordinates] = None
    category: Optional[Category] = None
    categoryGroup: Optional[CategoryGroup] = None  # Note: API uses categoryGroup, not categoryGroupCode
    chain: Optional[Chain] = None
    accommodationType: Optional[AccommodationType] = None
    boards: Optional[List[Board]] = None  # Note: API uses boards, not boardCodes
    segments: Optional[List[Segment]] = None  # Note: API uses segments, not segmentCodes
    address: Optional[Address] = None
    postalCode: Optional[str] = None
    city: Optional[Content] = None
    email: Optional[str] = None
    giataCode: Optional[int] = None  # Additional field from API
    phones: Optional[List[Phone]] = None
    web: Optional[str] = None
    lastUpdate: Optional[str] = None  # API returns string
    S2C: Optional[str] = None
    ranking: Optional[int] = None
    
    # Detailed information
    facilities: Optional[List[Facility]] = None
    terminals: Optional[List[Terminal]] = None
    interestPoints: Optional[List[InterestPoint]] = None
    images: Optional[List[Image]] = None
    wildcards: Optional[List[Wildcard]] = None
    rooms: Optional[List[Room]] = None
    issues: Optional[List[Issue]] = None

# Audit information for API responses
class AuditData(BaseModel):
    processTime: Optional[str] = None
    timestamp: Optional[str] = None
    requestHost: Optional[str] = None
    serverId: Optional[str] = None
    environment: Optional[str] = None
    release: Optional[str] = None
    token: Optional[str] = None
    internal: Optional[str] = None

# Hotels list response model
class HotelsResponse(BaseModel):
    auditData: Optional[AuditData] = None
    from_: Optional[int] = Field(None, alias="from")
    to: Optional[int] = None
    total: Optional[int] = None
    hotels: List[Hotel]

# Hotel details response model
class HotelDetailsResponse(BaseModel):
    auditData: Optional[AuditData] = None
    hotel: HotelDetails

# Error model for API errors
class Error(BaseModel):
    code: str
    message: str

# Error response model
class ErrorResponse(BaseModel):
    auditData: Optional[AuditData] = None
    error: Error